#!/usr/bin/env python
"""
Test script for HTML output.
"""
import os
import datetime
from optimus.cli import format_output_html

# Sample results
results = [
    {
        "cluster": "test-cluster",
        "namespace": "default",
        "pod": "test-pod",
        "cpu": {
            "stats": {
                "min": 100,
                "mean": 200,
                "median": 150,
                "p95": 300,
                "p99": 350,
                "max": 400
            },
            "current": {
                "request": 500,
                "limit": 1000
            },
            "recommended": {
                "request": 400,
                "limit": 800
            },
            "change": {
                "request": -20.0,
                "limit": -20.0
            },
            "ml_based": True,
            "containers": {
                "app": {
                    "stats": {
                        "min": 100,
                        "mean": 200,
                        "median": 150,
                        "p95": 300,
                        "p99": 350,
                        "max": 400
                    },
                    "current": {
                        "request": 500,
                        "limit": 1000
                    },
                    "recommended": {
                        "request": 400,
                        "limit": 800
                    },
                    "change": {
                        "request": -20.0,
                        "limit": -20.0
                    },
                    "ml_based": True
                }
            }
        },
        "memory": {
            "stats": {
                "min_mib": 100,
                "mean_mib": 200,
                "median_mib": 150,
                "p95_mib": 300,
                "p99_mib": 350,
                "max_mib": 400
            },
            "current": {
                "request_mib": 500,
                "limit_mib": 1000
            },
            "recommended": {
                "request_mib": 400,
                "limit_mib": 800
            },
            "change": {
                "request": -20.0,
                "limit": -20.0
            },
            "ml_based": True,
            "containers": {
                "app": {
                    "stats": {
                        "min_mib": 100,
                        "mean_mib": 200,
                        "median_mib": 150,
                        "p95_mib": 300,
                        "p99_mib": 350,
                        "max_mib": 400
                    },
                    "current": {
                        "request_mib": 500,
                        "limit_mib": 1000
                    },
                    "recommended": {
                        "request_mib": 400,
                        "limit_mib": 800
                    },
                    "change": {
                        "request": -20.0,
                        "limit": -20.0
                    },
                    "ml_based": True
                }
            }
        }
    }
]

# Sample chart paths
chart_paths = {
    "test-cluster_default_test-pod": {
        "cpu": "test-pod_cpu.png",
        "memory": "test-pod_memory.png",
        "comparison": "test-pod_comparison.png",
        "containers": {
            "app": {
                "cpu": "test-pod/containers/app_cpu_usage.png",
                "memory": "test-pod/containers/app_memory_usage.png",
                "comparison": "test-pod/containers/app_resource_comparison.png"
            }
        }
    }
}

# Generate HTML output
html_output = format_output_html(results, chart_paths, True)

# Write the output to a file
with open("test_report.html", "w") as f:
    f.write(html_output)

print(f"HTML report generated: {os.path.abspath('test_report.html')}")
