[project]
name = "optimus"
version = "0.1.0"
description = "Kubernetes resource optimization tool based on Prometheus metrics"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON>", email = "<EMAIL>" }
]
requires-python = ">=3.11"
dependencies = [
    "prometheus-api-client",
    "pandas",
    "numpy",
    "matplotlib",
    "pyyaml",
    "requests",
    "xgboost",
    "scikit-learn",
    "jinja2",
    "prophet",
    "anthropic",
    "google-generativeai",
    "fastapi>=0.115.12",
    "uvicorn>=0.34.2",
]

[project.scripts]
optimus = "optimus:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.optional-dependencies]
test = [
    "pytest",
    "pytest-cov",
    "pytest-mock",
]


[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
addopts = "-v --cov=optimus --cov-report=term-missing"
