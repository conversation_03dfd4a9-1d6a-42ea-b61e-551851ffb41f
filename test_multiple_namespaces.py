#!/usr/bin/env python
"""
Test script for multiple namespaces functionality.
"""
import os
import logging
from optimus.cli import main

# Configure logging
logging.basicConfig(level=logging.INFO)

# Set environment variables for multiple namespaces
os.environ["OPTIMUS_PROMETHEUS_URL"] = "http://localhost:9090"
os.environ["OPTIMUS_AUTH_HEADER_NAME"] = "X-API-KEY"
os.environ["OPTIMUS_AUTH_HEADER_VALUE"] = "your-api-key"
os.environ["OPTIMUS_NAMESPACE"] = "default,kube-system,monitoring"

# Run the main function with test arguments
args = [
    "--output-format", "html",
    "--output-file", "multiple_namespaces_report.html",
    "--generate-charts"
]

# Run the main function
main(args)

print(f"Multiple namespaces test report generated: multiple_namespaces_report.html")
