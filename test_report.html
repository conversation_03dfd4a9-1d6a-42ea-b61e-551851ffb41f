<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Optimus Resource Optimization Report</title>

    <!-- Cost calculation functions -->
    <script type="text/javascript">
        // Constants for cost calculation
        const CPU_COST_PER_CORE = 20.0; // $20 per core
        const MEMORY_COST_PER_GB = 5.0; // $5 per GB

        // Calculate monthly CPU cost
        function calculateMonthlyCpuCost(millicores) {
            if (millicores === null || millicores === undefined) return 0;
            // Convert millicores to cores (divide by 1000) and multiply by cost per core
            return ((millicores / 1000) * CPU_COST_PER_CORE).toFixed(2);
        }

        // Calculate monthly memory cost
        function calculateMonthlyMemoryCost(mib) {
            if (mib === null || mib === undefined) return 0;
            // Convert MiB to GB (divide by 1024) and multiply by cost per GB
            return ((mib / 1024) * MEMORY_COST_PER_GB).toFixed(2);
        }

        // Calculate total monthly cost
        function calculateTotalMonthlyCost(cpuMillicores, memoryMib) {
            const cpuCost = parseFloat(calculateMonthlyCpuCost(cpuMillicores));
            const memoryCost = parseFloat(calculateMonthlyMemoryCost(memoryMib));
            return (cpuCost + memoryCost).toFixed(2);
        }

        // Calculate savings percentage
        function calculateSavingsPercentage(currentCost, recommendedCost) {
            if (currentCost <= 0) return 0;
            return (((currentCost - recommendedCost) / currentCost) * 100).toFixed(1);
        }
    </script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        h1 {
            margin: 0;
            font-size: 28px;
        }
        h2 {
            color: #2c3e50;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        h3 { /* General style for H3, also affects H5 in containers if not overridden */
            color: #3498db;
            margin-top: 20px;
        }
        .timestamp {
            font-size: 14px;
            color: #bdc3c7;
            margin-top: 10px;
        }
        .pod-section {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        .pod-section > h2.collapsible-header {
            cursor: pointer;
            position: relative;
        }
        .pod-section > h2.collapsible-header::after {
            content: '+'; /* Plus sign */
            color: #2c3e50;
            font-weight: bold;
            float: right;
            margin-left: 5px;
        }
        .pod-section > h2.collapsible-header.active::after {
            content: "-"; /* Minus sign */
        }
        .collapsible-content {
            display: none; /* Collapsed by default */
            overflow: hidden;
            padding-top: 10px;
        }
        .resource-section {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .chart-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin: 20px 0;
        }
        .chart {
            width: 48%;
            margin-bottom: 20px;
            background-color: white;
            border-radius: 5px;
            padding: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .chart img {
            width: 100%;
            height: auto;
            border-radius: 5px;
        }
        .comparison-chart {
            width: 100%;
            margin-bottom: 20px;
            background-color: white;
            border-radius: 5px;
            padding: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .comparison-chart img {
            width: 100%;
            height: auto;
            border-radius: 5px;
        }
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .stats-table th, .stats-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e1e1e1;
        }
        .stats-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .stats-table tr:hover {
            background-color: #f5f5f5;
        }
        .recommendation {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        .container-section {
            margin-left: 20px;
            margin-bottom: 30px;
            padding: 15px;
            border-left: 3px solid #ecf0f1;
            background-color: #fafafa;
            border-radius: 0 5px 5px 0;
        }
        .container-section > h5 { /* Base style for h5 in containers */
            color: #3498db; /* Match h3 color */
            margin-top: 0; /* Container section already has padding */
            margin-bottom: 15px; /* Default bottom margin */
        }
        .container-section > h5.container-collapsible-header {
            cursor: pointer;
            position: relative;
            margin-bottom: 0; /* Remove bottom margin when collapsible, content div will space */
        }
        .container-section > h5.container-collapsible-header::after {
            content: '+'; /* Plus sign */
            color: #3498db; /* Match h5 text color */
            font-weight: bold;
            float: right;
            margin-left: 5px;
            font-size: 1em; /* Relative to h5 font size */
        }
        .container-section > h5.container-collapsible-header.active::after {
            content: "-"; /* Minus sign */
        }
        .container-collapsible-content {
            display: none; /* Collapsed by default */
            overflow: hidden;
            padding-top: 15px; /* Space below the h5 header */
        }
        .container-section .chart-container {
            margin: 15px 0;
        }
        .container-section .chart {
            width: 48%;
            margin-bottom: 15px;
        }
        .container-section .comparison-chart {
            width: 100%;
            margin-bottom: 15px;
        }
        .positive-change {
            color: #e74c3c;
        }
        .negative-change {
            color: #27ae60;
        }
        .neutral-change {
            color: #7f8c8d;
        }
        .ml-badge {
            display: inline-block;
            background-color: #9b59b6;
            color: white;
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 10px;
            margin-left: 10px;
            vertical-align: middle;
        }
        .summary {
            background-color: #f0f9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .cost-summary {
            background-color: #f9f0ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #9b59b6;
        }
        .cost-card {
            background-color: #fff;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: inline-block;
            width: 30%;
            margin-right: 1.5%;
            vertical-align: top;
        }
        .cost-card h3 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 16px;
        }
        .cost-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
            color: #2c3e50;
        }
        .cost-savings {
            color: #27ae60;
            font-weight: bold;
        }
        .cost-section {
            background-color: #f5f5f5;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        .cost-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .cost-table th, .cost-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e1e1e1;
        }
        .cost-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .cost-table .total-row {
            background-color: #f8f8f8;
            font-weight: bold;
        }
        .cost-table .total-row td {
            border-top: 2px solid #ddd;
        }

        /* Image modal/lightbox styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            margin: auto;
            display: block;
            max-width: 90%;
            max-height: 90%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 2px solid #fff;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
        }

        .close-modal {
            position: absolute;
            top: 15px;
            right: 25px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            transition: 0.3s;
            z-index: 1001;
        }

        .close-modal:hover,
        .close-modal:focus {
            color: #bbb;
            text-decoration: none;
            cursor: pointer;
        }

        .chart img, .comparison-chart img {
            cursor: pointer;
            transition: transform 0.2s;
        }

        .chart img:hover, .comparison-chart img:hover {
            transform: scale(1.03);
        }
        footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            color: #7f8c8d;
            font-size: 14px;
        }
    </style>
</head>
<body>
<header>
    <h1>Optimus Resource Optimization Report</h1>
    <div class="timestamp">Generated on: 2025-05-20 07:40:23</div>
</header>

<div class="summary">
    <h2>Summary</h2>
    <p>This report provides resource optimization recommendations for 1 pods across 1 namespaces.</p>
</div>

<div class="cost-summary">
    <h2>Cost Analysis</h2>
    <p>Based on a pricing model of $20 per CPU core and $5 per GB of memory per month.</p>

    <div class="cost-card">
        <h3>Current Monthly Spend</h3>
        <div class="cost-value" id="total-current-cost">$0.00</div>
        <p>Total cost of current resource allocation</p>
    </div>

    <div class="cost-card">
        <h3>Recommended Monthly Spend</h3>
        <div class="cost-value" id="total-recommended-cost">$0.00</div>
        <p>Total cost with recommended resource allocation</p>
    </div>

    <div class="cost-card">
        <h3>Potential Monthly Savings</h3>
        <div class="cost-value cost-savings" id="total-savings">$0.00 (0%)</div>
        <p>Estimated savings with recommended resources</p>
    </div>
</div>


<div class="pod-section">
    <h2 class="collapsible-header">default/test-pod</h2>
    <div class="collapsible-content"> {/* Content for Pod Section */}
        <div class="chart-container">
            
            
            <div class="chart">
                <h3>CPU Usage Analysis</h3>
                <img src="charts/test-cluster_default_test-pod/test-pod_cpu.png" alt="CPU Usage Analysis">
            </div>
            

            
            <div class="chart">
                <h3>Memory Usage Analysis</h3>
                <img src="charts/test-cluster_default_test-pod/test-pod_memory.png" alt="Memory Usage Analysis">
            </div>
            

            
            <div class="comparison-chart">
                <h3>Resource Comparison</h3>
                <img src="charts/test-cluster_default_test-pod/test-pod_comparison.png" alt="Resource Comparison">
            </div>
            
            
        </div>

        <!-- Pod Cost Analysis Section -->
        <div class="cost-section">
            <h3>Cost Analysis</h3>
            <p>Monthly cost estimates based on $20 per CPU core and $5 per GB of memory.</p>

            <table class="cost-table">
                <tr>
                    <th>Resource</th>
                    <th>Current Allocation</th>
                    <th>Current Monthly Cost</th>
                    <th>Recommended Allocation</th>
                    <th>Recommended Monthly Cost</th>
                    <th>Monthly Savings</th>
                </tr>
                <tr>
                    <td>CPU</td>
                    <td>500 millicores</td>
                    <td class="pod-cpu-current-cost" data-cpu="500">$0.00</td>
                    <td>400 millicores</td>
                    <td class="pod-cpu-recommended-cost" data-cpu="400">$0.00</td>
                    <td class="pod-cpu-savings">$0.00 (0%)</td>
                </tr>
                <tr>
                    <td>Memory</td>
                    <td>500 MiB</td>
                    <td class="pod-memory-current-cost" data-memory="500">$0.00</td>
                    <td>400 MiB</td>
                    <td class="pod-memory-recommended-cost" data-memory="400">$0.00</td>
                    <td class="pod-memory-savings">$0.00 (0%)</td>
                </tr>
                <tr class="total-row">
                    <td><strong>Total</strong></td>
                    <td></td>
                    <td class="pod-total-current-cost">$0.00</td>
                    <td></td>
                    <td class="pod-total-recommended-cost">$0.00</td>
                    <td class="pod-total-savings">$0.00 (0%)</td>
                </tr>
            </table>
        </div>

        
        <div class="resource-section">
            <h3>CPU Analysis <span class="ml-badge">ML-based</span></h3>
            <h4>Pod-level Summary</h4>
            <table class="stats-table">
                <tr><th>Statistic</th><th>Value (millicores)</th></tr>
                <tr><td>Minimum</td><td>100</td></tr>
                <tr><td>Mean</td><td>200</td></tr>
                <tr><td>Median</td><td>150</td></tr>
                <tr><td>95th Percentile</td><td>300</td></tr>
                <tr><td>99th Percentile</td><td>350</td></tr>
                <tr><td>Maximum</td><td>400</td></tr>
            </table>
            <div class="recommendation">
                <h4>Resource Settings</h4>
                <table class="stats-table">
                    <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                    <tr>
                        <td>Request</td>
                        <td>500 millicores</td>
                        <td>400 millicores</td>
                        <td class="negative-change">
                            -20.0%
                        </td>
                    </tr>
                    <tr>
                        <td>Limit</td>
                        <td>1000 millicores</td>
                        <td>800 millicores</td>
                        <td class="negative-change">
                            -20.0%
                        </td>
                    </tr>
                </table>
            </div>

            
            <h4>Container-level Analysis</h4>
            
            <div class="container-section">
                <h5 class="container-collapsible-header">app <span class="ml-badge">ML-based</span></h5>
                <div class="container-collapsible-content"> {/* Content for CPU Container Section */}
                    
                    <div class="chart-container">
                        
                        <div class="chart">
                            <h3>CPU Usage Analysis</h3>
                            <img src="charts/test-cluster_default_test-pod/containers/app_cpu_usage.png" alt="CPU Usage Analysis for app">
                        </div>
                        
                        
                        <div class="chart">
                            <h3>Memory Usage Analysis</h3>
                            <img src="charts/test-cluster_default_test-pod/containers/app_memory_usage.png" alt="Memory Usage Analysis for app">
                        </div>
                        
                        
                        <div class="comparison-chart">
                            <h3>Resource Comparison</h3>
                            <img src="charts/test-cluster_default_test-pod/containers/app_resource_comparison.png" alt="Resource Comparison for app">
                        </div>
                        
                    </div>
                    
                    <table class="stats-table">
                        <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                        <tr>
                            <td>Request</td>
                            <td>500 millicores</td>
                            <td>400 millicores</td>
                            <td class="negative-change">
                                -20.0%
                            </td>
                        </tr>
                        <tr>
                            <td>Limit</td>
                            <td>1000 millicores</td>
                            <td>800 millicores</td>
                            <td class="negative-change">
                                -20.0%
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            
        </div>
        

        
        <div class="resource-section">
            <h3>Memory Analysis <span class="ml-badge">ML-based</span></h3>
            <h4>Pod-level Summary</h4>
            <table class="stats-table">
                <tr><th>Statistic</th><th>Value (MiB)</th></tr>
                <tr><td>Minimum</td><td>100</td></tr>
                <tr><td>Mean</td><td>200</td></tr>
                <tr><td>Median</td><td>150</td></tr>
                <tr><td>95th Percentile</td><td>300</td></tr>
                <tr><td>99th Percentile</td><td>350</td></tr>
                <tr><td>Maximum</td><td>400</td></tr>
            </table>
            <div class="recommendation">
                <h4>Resource Settings</h4>
                <table class="stats-table">
                    <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                    <tr>
                        <td>Request</td>
                        <td>500 MiB</td>
                        <td>400 MiB</td>
                        <td class="negative-change">
                            -20.0%
                        </td>
                    </tr>
                    <tr>
                        <td>Limit</td>
                        <td>1000 MiB</td>
                        <td>800 MiB</td>
                        <td class="negative-change">
                            -20.0%
                        </td>
                    </tr>
                </table>
            </div>

            
            <h4>Container-level Analysis</h4>
            
            <div class="container-section">
                <h5 class="container-collapsible-header">app <span class="ml-badge">ML-based</span></h5>
                <div class="container-collapsible-content"> {/* Content for Memory Container Section */}
                    
                    <div class="chart-container">
                        
                        <div class="chart">
                            <h3>CPU Usage Analysis</h3>
                            <img src="charts/test-cluster_default_test-pod/containers/app_cpu_usage.png" alt="CPU Usage Analysis for app">
                        </div>
                        
                        
                        <div class="chart">
                            <h3>Memory Usage Analysis</h3>
                            <img src="charts/test-cluster_default_test-pod/containers/app_memory_usage.png" alt="Memory Usage Analysis for app">
                        </div>
                        
                        
                        <div class="comparison-chart">
                            <h3>Resource Comparison</h3>
                            <img src="charts/test-cluster_default_test-pod/containers/app_resource_comparison.png" alt="Resource Comparison for app">
                        </div>
                        
                    </div>
                    
                    <table class="stats-table">
                        <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                        <tr>
                            <td>Request</td>
                            <td>500 MiB</td>
                            <td>400 MiB</td>
                            <td class="negative-change">
                                -20.0%
                            </td>
                        </tr>
                        <tr>
                            <td>Limit</td>
                            <td>1000 MiB</td>
                            <td>800 MiB</td>
                            <td class="negative-change">
                                -20.0%
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            
        </div>
        
    </div> 
</div>


<!-- Image Modal/Lightbox -->
<div id="imageModal" class="modal">
    <span class="close-modal">&times;</span>
    <img class="modal-content" id="modalImage">
</div>

<footer>
    <p>Generated by Optimus Resource Optimizer</p>
</footer>

<script type="text/javascript">
    // Calculate costs when the page loads
    document.addEventListener('DOMContentLoaded', function() {
        calculateAllCosts();
    });

    // Calculate costs for all pods
    function calculateAllCosts() {
        let totalCurrentCost = 0;
        let totalRecommendedCost = 0;

        // Process each pod
        document.querySelectorAll('.pod-section').forEach(function(podSection) {
            // Calculate CPU costs
            const cpuCurrentElements = podSection.querySelectorAll('.pod-cpu-current-cost');
            const cpuRecommendedElements = podSection.querySelectorAll('.pod-cpu-recommended-cost');
            const cpuSavingsElements = podSection.querySelectorAll('.pod-cpu-savings');

            cpuCurrentElements.forEach(function(element, index) {
                const cpuMillicores = parseFloat(element.getAttribute('data-cpu') || 0);
                const cpuCurrentCost = parseFloat(calculateMonthlyCpuCost(cpuMillicores));
                element.textContent = '$' + cpuCurrentCost.toFixed(2);

                const recommendedElement = cpuRecommendedElements[index];
                const recommendedCpuMillicores = parseFloat(recommendedElement.getAttribute('data-cpu') || 0);
                const cpuRecommendedCost = parseFloat(calculateMonthlyCpuCost(recommendedCpuMillicores));
                recommendedElement.textContent = '$' + cpuRecommendedCost.toFixed(2);

                const cpuSavings = cpuCurrentCost - cpuRecommendedCost;
                const cpuSavingsPercent = calculateSavingsPercentage(cpuCurrentCost, cpuRecommendedCost);
                cpuSavingsElements[index].textContent = '$' + cpuSavings.toFixed(2) + ' (' + cpuSavingsPercent + '%)';
                if (cpuSavings > 0) {
                    cpuSavingsElements[index].classList.add('cost-savings');
                }
            });

            // Calculate Memory costs
            const memoryCurrentElements = podSection.querySelectorAll('.pod-memory-current-cost');
            const memoryRecommendedElements = podSection.querySelectorAll('.pod-memory-recommended-cost');
            const memorySavingsElements = podSection.querySelectorAll('.pod-memory-savings');

            memoryCurrentElements.forEach(function(element, index) {
                const memoryMiB = parseFloat(element.getAttribute('data-memory') || 0);
                const memoryCurrentCost = parseFloat(calculateMonthlyMemoryCost(memoryMiB));
                element.textContent = '$' + memoryCurrentCost.toFixed(2);

                const recommendedElement = memoryRecommendedElements[index];
                const recommendedMemoryMiB = parseFloat(recommendedElement.getAttribute('data-memory') || 0);
                const memoryRecommendedCost = parseFloat(calculateMonthlyMemoryCost(recommendedMemoryMiB));
                recommendedElement.textContent = '$' + memoryRecommendedCost.toFixed(2);

                const memorySavings = memoryCurrentCost - memoryRecommendedCost;
                const memorySavingsPercent = calculateSavingsPercentage(memoryCurrentCost, memoryRecommendedCost);
                memorySavingsElements[index].textContent = '$' + memorySavings.toFixed(2) + ' (' + memorySavingsPercent + '%)';
                if (memorySavings > 0) {
                    memorySavingsElements[index].classList.add('cost-savings');
                }
            });

            // Calculate pod totals
            const podTotalCurrentElements = podSection.querySelectorAll('.pod-total-current-cost');
            const podTotalRecommendedElements = podSection.querySelectorAll('.pod-total-recommended-cost');
            const podTotalSavingsElements = podSection.querySelectorAll('.pod-total-savings');

            podTotalCurrentElements.forEach(function(element, index) {
                const cpuCurrentCost = parseFloat(cpuCurrentElements[index]?.textContent.replace('$', '') || 0);
                const memoryCurrentCost = parseFloat(memoryCurrentElements[index]?.textContent.replace('$', '') || 0);
                const podTotalCurrent = cpuCurrentCost + memoryCurrentCost;
                element.textContent = '$' + podTotalCurrent.toFixed(2);

                const cpuRecommendedCost = parseFloat(cpuRecommendedElements[index]?.textContent.replace('$', '') || 0);
                const memoryRecommendedCost = parseFloat(memoryRecommendedElements[index]?.textContent.replace('$', '') || 0);
                const podTotalRecommended = cpuRecommendedCost + memoryRecommendedCost;
                podTotalRecommendedElements[index].textContent = '$' + podTotalRecommended.toFixed(2);

                const podTotalSavings = podTotalCurrent - podTotalRecommended;
                const podTotalSavingsPercent = calculateSavingsPercentage(podTotalCurrent, podTotalRecommended);
                podTotalSavingsElements[index].textContent = '$' + podTotalSavings.toFixed(2) + ' (' + podTotalSavingsPercent + '%)';
                if (podTotalSavings > 0) {
                    podTotalSavingsElements[index].classList.add('cost-savings');
                }

                // Add to grand totals
                totalCurrentCost += podTotalCurrent;
                totalRecommendedCost += podTotalRecommended;
            });
        });

        // Update summary totals
        document.getElementById('total-current-cost').textContent = '$' + totalCurrentCost.toFixed(2);
        document.getElementById('total-recommended-cost').textContent = '$' + totalRecommendedCost.toFixed(2);

        const totalSavings = totalCurrentCost - totalRecommendedCost;
        const totalSavingsPercent = calculateSavingsPercentage(totalCurrentCost, totalRecommendedCost);
        document.getElementById('total-savings').textContent = '$' + totalSavings.toFixed(2) + ' (' + totalSavingsPercent + '%)';
    }

    // Image modal/lightbox functionality
    const modal = document.getElementById('imageModal');
    const modalImg = document.getElementById('modalImage');
    const closeModal = document.querySelector('.close-modal');

    // Get all chart images and add click event listeners
    document.addEventListener('DOMContentLoaded', function() {
        // Find all chart images
        const chartImages = document.querySelectorAll('.chart img, .comparison-chart img');

        // Add click event to each image
        chartImages.forEach(function(img) {
            img.addEventListener('click', function() {
                modal.style.display = 'block';
                modalImg.src = this.src;
                // Add alt text if available
                modalImg.alt = this.alt || '';
            });
        });

        // Close modal when clicking the × button
        closeModal.addEventListener('click', function() {
            modal.style.display = 'none';
        });

        // Close modal when clicking outside the image
        modal.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && modal.style.display === 'block') {
                modal.style.display = 'none';
            }
        });
    });
</script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        function setupCollapsible(headerSelector, activeClassName) {
            var headers = document.querySelectorAll(headerSelector);
            headers.forEach(function(header) {
                header.addEventListener('click', function() {
                    this.classList.toggle(activeClassName);
                    var content = this.nextElementSibling;
                    if (content.style.display === "block") {
                        content.style.display = "none";
                    } else {
                        content.style.display = "block";
                    }
                });
            });
        }

        setupCollapsible('.collapsible-header', 'active'); // For Pod Sections (h2)
        setupCollapsible('.container-collapsible-header', 'active'); // For Container Sections (h5)
    });
</script>
</body>
</html>