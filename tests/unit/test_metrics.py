"""
Tests for the metrics module.
"""
import json
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

import pandas as pd
import pytest
import requests

from optimus.config import Config
from optimus.metrics import PrometheusClient, KubernetesMetrics


class TestPrometheusClient:
    """Tests for the PrometheusClient class."""

    @pytest.fixture
    def config(self):
        """Create a test configuration."""
        config = MagicMock(spec=Config)
        config.prometheus_url = "http://prometheus:9090"
        config.get_auth_headers.return_value = {"x-request-header": "test-value"}
        return config

    @pytest.fixture
    def client(self, config):
        """Create a test client."""
        return PrometheusClient(config)

    def test_init(self, client, config):
        """Test initialization."""
        assert client.base_url == "http://prometheus:9090"
        assert client.headers == {"x-request-header": "test-value"}

    def test_query_range(self, client):
        """Test query_range method."""
        start_time = datetime.now() - timedelta(hours=1)
        end_time = datetime.now()

        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "success", "data": {"result": []}}

        with patch.object(requests, "get", return_value=mock_response) as mock_get:
            result = client.query_range("test_query", start_time, end_time, "1m")

            mock_get.assert_called_once()
            args, kwargs = mock_get.call_args
            assert args[0] == "http://prometheus:9090/api/v1/query_range"
            assert kwargs["params"]["query"] == "test_query"
            assert kwargs["params"]["start"] == start_time.timestamp()
            assert kwargs["params"]["end"] == end_time.timestamp()
            assert kwargs["params"]["step"] == "1m"
            assert kwargs["headers"] == {"x-request-header": "test-value"}

            assert result == {"status": "success", "data": {"result": []}}

    def test_query(self, client):
        """Test query method."""
        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "success", "data": {"result": []}}

        with patch.object(requests, "get", return_value=mock_response) as mock_get:
            result = client.query("test_query")

            mock_get.assert_called_once()
            args, kwargs = mock_get.call_args
            assert args[0] == "http://prometheus:9090/api/v1/query"
            assert kwargs["params"]["query"] == "test_query"
            assert kwargs["headers"] == {"x-request-header": "test-value"}

            assert result == {"status": "success", "data": {"result": []}}


class TestKubernetesMetrics:
    """Tests for the KubernetesMetrics class."""

    @pytest.fixture
    def config(self):
        """Create a test configuration."""
        config = MagicMock(spec=Config)
        config.prometheus_url = "http://prometheus:9090"
        config.get_auth_headers.return_value = {"X-API-KEY": "test-value"}
        config.time_period_hours = 24
        config.namespace = "default"
        config.namespaces = ["default"]
        config.cluster = "test-cluster"
        config.pod_label_selector = "app=myapp"
        return config

    @pytest.fixture
    def prometheus_client(self):
        """Create a mock Prometheus client."""
        return MagicMock(spec=PrometheusClient)

    @pytest.fixture
    def metrics(self, config):
        """Create a test metrics collector with a mock Prometheus client."""
        metrics = KubernetesMetrics(config)
        metrics.prometheus = MagicMock(spec=PrometheusClient)
        return metrics

    def test_get_time_range(self, metrics):
        """Test get_time_range method."""
        start_time, end_time = metrics.get_time_range()
        assert end_time - start_time == timedelta(hours=24)

    def test_get_pods_single_namespace(self, metrics):
        """Test get_pods method."""
        # Mock the Prometheus query response
        metrics.prometheus.query.return_value = {
            "status": "success",
            "data": {
                "result": [
                    {
                        "metric": {
                            "pod_phase": "Running",
                            "daemonset": "calico-node",
                            "sts_host": "rna-sa-playground-0-sa-playground",
                            "priority_class": "system-node-critical",
                            "interval": "0",
                            "__name__": "kubernetes_state_pod_age",
                            "uid": "7fc1058b-e9f3-4f50-b5ac-aa0de839f259",
                            "node": "node-1",
                            "kube_app_name": "calico-node",
                            "namespace": "default",
                            "cluster_name": "sa-playground",
                            "pod_name": "test-pod-abc123",
                        },
                        "value": [
                            1747664694,
                            "2257404"
                        ]
                    },
                    {
                        "metric": {
                            "deployment": "calico-kube-controllers",
                            "pod_phase": "Running",
                            "sts_host": "rna-sa-playground-0-sa-playground",
                            "priority_class": "system-cluster-critical",
                            "interval": "0",
                            "uid": "b9755a49-ab18-4de2-a63d-c9164cc1331b",
                            "node": "node-2",
                            "kube_app_name": "calico-kube-controllers",
                            "namespace": "default",
                            "cluster_name": "sa-playground",
                            "pod_name": "test-pod-def456",
                            "replicaset": "calico-kube-controllers-69647b4d4c",
                            "__name__": "kubernetes_state_pod_age"
                        },
                        "value": [
                            1747664694,
                            "2257404"
                        ]
                    },
                ],
            },
        }

        pods = metrics.get_pods()

        # Check that the query was called with the correct parameters
        metrics.prometheus.query.assert_called_once_with('kubernetes_state_pod_age{namespace="default",cluster_name="test-cluster"}')

        # Check the result
        # We only get one pod because the second one has the same base name after removing the suffix
        assert len(pods) == 1
        assert pods[0]["namespace"] == "default"
        assert pods[0]["pod"] == "test-pod"

    def test_get_pods_multiple_namespaces(self, metrics, config):
        """Test get_pods method with multiple namespaces."""
        # Set multiple namespaces
        config.namespaces = ["default", "kube-system", "monitoring"]

        # Mock the Prometheus query response
        metrics.prometheus.query.return_value = {
            "status": "success",
            "data": {
                "result": [
                    {
                        "metric": {
                            "pod_phase": "Running",
                            "interval": "0",
                            "__name__": "kubernetes_state_pod_age",
                            "uid": "7fc1058b-e9f3-4f50-b5ac-aa0de839f259",
                            "node": "node-1",
                            "namespace": "default",
                            "cluster_name": "sa-playground",
                            "pod_name": "app-pod-abc123",
                        },
                        "value": [1617293340, "1"]
                    },
                    {
                        "metric": {
                            "pod_phase": "Running",
                            "interval": "0",
                            "__name__": "kubernetes_state_pod_age",
                            "uid": "8fc1058b-e9f3-4f50-b5ac-aa0de839f260",
                            "node": "node-2",
                            "namespace": "kube-system",
                            "cluster_name": "sa-playground",
                            "pod_name": "kube-proxy-def456",
                        },
                        "value": [1617293340, "1"]
                    },
                    {
                        "metric": {
                            "pod_phase": "Running",
                            "interval": "0",
                            "__name__": "kubernetes_state_pod_age",
                            "uid": "9fc1058b-e9f3-4f50-b5ac-aa0de839f261",
                            "node": "node-3",
                            "namespace": "monitoring",
                            "cluster_name": "sa-playground",
                            "pod_name": "prometheus-ghi789",
                        },
                        "value": [1617293340, "1"]
                    }
                ]
            }
        }

        pods = metrics.get_pods()

        # Check that the query was called with the correct arguments
        metrics.prometheus.query.assert_called_once_with(
            'kubernetes_state_pod_age{namespace=~"(default|kube-system|monitoring)",cluster_name="test-cluster"}'
        )

        # Check that the pods were returned correctly
        assert len(pods) == 3

        # Check first pod
        assert pods[0]["namespace"] == "default"
        assert pods[0]["pod"] == "app-pod"
        assert pods[0]["full_pod_name"] == "app-pod-abc123"

        # Check second pod
        assert pods[1]["namespace"] == "kube-system"
        assert pods[1]["pod"] == "kube-proxy"
        assert pods[1]["full_pod_name"] == "kube-proxy-def456"

        # Check third pod
        assert pods[2]["namespace"] == "monitoring"
        assert pods[2]["pod"] == "prometheus"
        assert pods[2]["full_pod_name"] == "prometheus-ghi789"

    def test_get_cpu_usage(self, metrics):
        """Test get_cpu_usage method."""
        # Mock the Prometheus query_range response with container information
        metrics.prometheus.query_range.return_value = {
            "status": "success",
            "data": {
                "result": [
                    {
                        "metric": {"namespace": "default", "pod": "test-pod", "container": "app"},
                        "values": [
                            [1600000000, "0.1"],
                            [1600000300, "0.2"],
                        ],
                    },
                ],
            },
        }

        # Mock the get_time_range method
        start_time = datetime.fromtimestamp(1600000000)
        end_time = datetime.fromtimestamp(1600000300)
        with patch.object(metrics, "get_time_range", return_value=(start_time, end_time)):
            df = metrics.get_cpu_usage("test-cluster", "default", "test-pod")

        # Check that the query was called with the correct parameters
        metrics.prometheus.query_range.assert_called_once()
        args, kwargs = metrics.prometheus.query_range.call_args
        assert args[0] == 'sum by (cluster_name, namespace, container) (container_cpu_usage{cluster_name="test-cluster", namespace="default", pod_name=~"^test-pod(-[a-z0-9]+)+$|^test-pod-[a-z0-9]+$", container!="POD", container!=""}[5m]) / 1000000000'
        assert args[1] == start_time
        assert args[2] == end_time
        assert kwargs["step"] == "5m"

        # Check the result
        assert not df.empty
        assert "cpu_usage_cores" in df.columns
        assert df["cpu_usage_cores"].tolist() == [0.1, 0.2]

    def test_get_memory_usage(self, metrics):
        """Test get_memory_usage method."""
        # Mock the Prometheus query_range response with container information
        metrics.prometheus.query_range.return_value = {
            "status": "success",
            "data": {
                "result": [
                    {
                        "metric": {"namespace": "default", "pod": "test-pod", "container": "app"},
                        "values": [
                            [1600000000, "104857600"],  # 100 MiB
                            [1600000300, "209715200"],  # 200 MiB
                        ],
                    },
                ],
            },
        }

        # Mock the get_time_range method
        start_time = datetime.fromtimestamp(1600000000)
        end_time = datetime.fromtimestamp(1600000300)
        with patch.object(metrics, "get_time_range", return_value=(start_time, end_time)):
            df = metrics.get_memory_usage("test-cluster", "default", "test-pod")

        # Check that the query was called with the correct parameters
        metrics.prometheus.query_range.assert_called_once()
        args, kwargs = metrics.prometheus.query_range.call_args
        assert args[0] == 'sum by (cluster_name, namespace, container) (container_memory_usage{cluster_name="test-cluster", namespace="default", pod_name=~"^test-pod(-[a-z0-9]+)+$|^test-pod-[a-z0-9]+$", container!="POD", container!=""}[5m])'
        assert args[1] == start_time
        assert args[2] == end_time
        assert kwargs["step"] == "5m"

        # Check the result
        assert not df.empty
        assert "memory_usage_bytes" in df.columns
        assert "memory_usage_mib" in df.columns
        assert df["memory_usage_bytes"].tolist() == [104857600, 209715200]
        assert df["memory_usage_mib"].tolist() == [100.0, 200.0]

    def test_extract_base_pod_name(self, metrics):
        """Test _extract_base_pod_name method."""
        # Test deployment pattern
        assert metrics._extract_base_pod_name("nginx-3e8f7d4b9c-x5jvl") == "nginx"

        # Test statefulset pattern
        assert metrics._extract_base_pod_name("postgres-0") == "postgres"

        # Test daemonset pattern
        assert metrics._extract_base_pod_name("fluentd-78bdc87f8c") == "fluentd"

        # Test logs-agent pattern
        assert metrics._extract_base_pod_name("logs-agent-abc123") == "logs-agent"

        # Test node-agent pattern
        assert metrics._extract_base_pod_name("node-agent-xyz789") == "node-agent"

        # Test more complex daemonset patterns
        assert metrics._extract_base_pod_name("calico-node-abc123") == "calico-node"
        assert metrics._extract_base_pod_name("kube-proxy-def456") == "kube-proxy"

    def test_get_pod_count(self, metrics):
        """Test get_pod_count method."""
        # Mock the Prometheus query response
        pod_count_response = {
            "status": "success",
            "data": {
                "result": [
                    {
                        "value": [1600000000, "3"],  # 3 pods
                    },
                ],
            },
        }
        metrics.prometheus.query.return_value = pod_count_response

        # Call the method
        pod_count = metrics.get_pod_count("test-cluster", "default", "test-pod")

        # Check the result
        assert pod_count == 3

        # Check that the query was called with the correct parameters
        metrics.prometheus.query.assert_called_once()
        query = metrics.prometheus.query.call_args[0][0]
        assert "test-cluster" in query
        assert "default" in query
        assert "test-pod" in query

        # Test with empty result
        metrics.prometheus.query.reset_mock()
        metrics.prometheus.query.return_value = {"status": "success", "data": {"result": []}}
        pod_count = metrics.get_pod_count("test-cluster", "default", "test-pod")
        assert pod_count == 1  # Default to 1 if no pods found

    def test_get_resource_requests_and_limits(self, metrics):
        """Test get_resource_requests_and_limits method."""
        # Mock the Prometheus query responses with container information
        cpu_request_response = {
            "status": "success",
            "data": {
                "result": [
                    {
                        "metric": {"container": "app"},
                        "value": [1600000000, "0.5"],
                    },
                ],
            },
        }
        cpu_limit_response = {
            "status": "success",
            "data": {
                "result": [
                    {
                        "metric": {"container": "app"},
                        "value": [1600000000, "1.0"],
                    },
                ],
            },
        }
        memory_request_response = {
            "status": "success",
            "data": {
                "result": [
                    {
                        "metric": {"container": "app"},
                        "value": [1600000000, "104857600"],  # 100 MiB
                    },
                ],
            },
        }
        memory_limit_response = {
            "status": "success",
            "data": {
                "result": [
                    {
                        "metric": {"container": "app"},
                        "value": [1600000000, "209715200"],  # 200 MiB
                    },
                ],
            },
        }

        # Mock response for pod count query
        pod_count_response = {
            "status": "success",
            "data": {
                "result": [
                    {
                        "value": [1600000000, "2"],  # 2 pods
                    },
                ],
            },
        }

        metrics.prometheus.query.side_effect = [
            cpu_request_response,
            cpu_limit_response,
            memory_request_response,
            memory_limit_response,
            pod_count_response,  # For get_pod_count
        ]

        resources = metrics.get_resource_requests_and_limits("test-cluster", "default", "test-pod")

        # Check that the queries were called with the correct parameters
        assert metrics.prometheus.query.call_count == 5  # 4 resource queries + 1 pod count query

        # Check the container-level results
        assert "containers" in resources
        assert "app" in resources["containers"]
        container = resources["containers"]["app"]

        # Values should be divided by 2 (pod count)
        assert container["cpu_request"] == 0.25  # 0.5 / 2
        assert container["cpu_limit"] == 0.5  # 1.0 / 2
        assert container["memory_request_bytes"] == 52428800  # 104857600 / 2
        assert container["memory_limit_bytes"] == 104857600  # 209715200 / 2
        assert container["memory_request_mib"] == 50.0  # 100.0 / 2
        assert container["memory_limit_mib"] == 100.0  # 200.0 / 2

        # Check the aggregated results (also divided by pod count)
        assert resources["cpu_request"] == 0.25  # 0.5 / 2
        assert resources["cpu_limit"] == 0.5  # 1.0 / 2
        assert resources["memory_request_bytes"] == 52428800  # 104857600 / 2
        assert resources["memory_limit_bytes"] == 104857600  # 209715200 / 2
        assert resources["memory_request_mib"] == 50.0  # 100.0 / 2
        assert resources["memory_limit_mib"] == 100.0  # 200.0 / 2
