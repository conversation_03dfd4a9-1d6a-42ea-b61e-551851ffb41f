"""
Tests for the analyzer module.
"""
from unittest.mock import patch, MagicMock

import pandas as pd
import pytest

from optimus.analyzer import ResourceAnalyzer
from optimus.config import Config
from optimus.metrics import KubernetesMetrics
from optimus.ml import ResourcePredictor


class TestResourceAnalyzer:
    """Tests for the ResourceAnalyzer class."""

    @pytest.fixture
    def config(self):
        """Create a test configuration."""
        config = MagicMock(spec=Config)
        config.cpu_headroom_percent = 20.0
        config.memory_headroom_percent = 30.0
        config.prometheus_url = "http://prometheus:9090"
        config.get_auth_headers.return_value = {"x-request-header": "test-value"}
        config.ml_model_type = "xgboost"  # Add the ML model type
        return config

    @pytest.fixture
    def metrics(self):
        """Create a mock metrics collector."""
        return MagicMock(spec=KubernetesMetrics)

    @pytest.fixture
    def analyzer(self, config):
        """Create a test analyzer with a mock metrics collector."""
        analyzer = ResourceAnalyzer(config)
        analyzer.metrics = MagicMock(spec=KubernetesMetrics)
        return analyzer

    def test_analyze_pod(self, analyzer):
        """Test analyze_pod method."""
        # Mock the metrics methods with container information
        cpu_df = pd.DataFrame({
            "timestamp": pd.date_range(start="2023-01-01", periods=10, freq="5min"),
            "cpu_usage_cores": [0.1, 0.2, 0.3, 0.4, 0.5, 0.4, 0.3, 0.2, 0.1, 0.2],
            "container": ["app"] * 10,  # Add container column
        })

        memory_df = pd.DataFrame({
            "timestamp": pd.date_range(start="2023-01-01", periods=10, freq="5min"),
            "memory_usage_bytes": [104857600, 157286400, 209715200, 262144000, 314572800,
                                  262144000, 209715200, 157286400, 104857600, 157286400],
            "memory_usage_mib": [100, 150, 200, 250, 300, 250, 200, 150, 100, 150],
            "container": ["app"] * 10,  # Add container column
        })

        resources = {
            "cpu_request": 0.5,
            "cpu_limit": 1.0,
            "memory_request_bytes": 209715200,  # 200 MiB
            "memory_limit_bytes": 314572800,    # 300 MiB
            "memory_request_mib": 200,
            "memory_limit_mib": 300,
            "containers": {  # Add container-level resources
                "app": {
                    "cpu_request": 0.5,
                    "cpu_limit": 1.0,
                    "memory_request_bytes": 209715200,  # 200 MiB
                    "memory_limit_bytes": 314572800,    # 300 MiB
                    "memory_request_mib": 200,
                    "memory_limit_mib": 300,
                }
            }
        }

        analyzer.metrics.get_cpu_usage.return_value = cpu_df
        analyzer.metrics.get_memory_usage.return_value = memory_df
        analyzer.metrics.get_resource_requests_and_limits.return_value = resources

        result = analyzer.analyze_pod("test-cluster", "default", "test-pod")

        # Check that the metrics methods were called with the correct parameters
        # Note: With ML enabled, get_cpu_usage and get_memory_usage are called twice (once for regular analysis, once for extended data)
        analyzer.metrics.get_cpu_usage.assert_any_call("test-cluster", "default", "test-pod")
        analyzer.metrics.get_memory_usage.assert_any_call("test-cluster", "default", "test-pod")
        analyzer.metrics.get_resource_requests_and_limits.assert_called_once_with("test-cluster", "default", "test-pod")

        # Check the result structure
        assert result["namespace"] == "default"
        assert result["pod"] == "test-pod"
        assert "cpu" in result
        assert "memory" in result
        assert "current_resources" in result

        # Check CPU analysis
        cpu = result["cpu"]
        assert "stats" in cpu
        assert "current" in cpu
        assert "recommended" in cpu
        assert "change" in cpu

        # Check CPU stats (values are now in millicores)
        assert cpu["stats"]["min"] == 100.0  # 0.1 cores = 100 millicores
        assert cpu["stats"]["max"] == 500.0  # 0.5 cores = 500 millicores
        assert 450.0 <= cpu["stats"]["p95"] <= 500.0  # 0.45-0.5 cores = 450-500 millicores

        # Check CPU recommendations (values are now in millicores)
        assert 540.0 <= cpu["recommended"]["request"] <= 600.0  # 0.5 * 1.2 * 1000
        assert 600.0 <= cpu["recommended"]["limit"] <= 900.0    # 0.5 * 1.2 * 1000

        # Check memory analysis
        memory = result["memory"]
        assert "stats" in memory
        assert "current" in memory
        assert "recommended" in memory
        assert "change" in memory

        # Check memory stats
        assert memory["stats"]["min_mib"] == 100
        assert memory["stats"]["max_mib"] == 300
        assert 275 <= memory["stats"]["p95_mib"] <= 300

        # Check memory recommendations
        assert 360 <= memory["recommended"]["request_mib"] <= 390  # 300 * 1.3
        assert 390 <= memory["recommended"]["limit_mib"] <= 450    # 300 * 1.3

    def test_analyze_all_pods(self, analyzer):
        """Test analyze_all_pods method."""
        # Mock the get_pods method
        analyzer.metrics.get_pods.return_value = [
            {"cluster": "test-cluster", "namespace": "default", "pod": "test-pod-1", "node": "node-1"},
            {"cluster": "test-cluster", "namespace": "default", "pod": "test-pod-2", "node": "node-2"},
        ]

        # Mock the analyze_pod method
        pod1_result = {"namespace": "default", "pod": "test-pod-1", "cpu": {}, "memory": {}}
        pod2_result = {"namespace": "default", "pod": "test-pod-2", "cpu": {}, "memory": {}}
        analyzer.analyze_pod = MagicMock(side_effect=[pod1_result, pod2_result])

        results = analyzer.analyze_all_pods()

        # Check that the methods were called with the correct parameters
        analyzer.metrics.get_pods.assert_called_once()
        assert analyzer.analyze_pod.call_count == 2
        analyzer.analyze_pod.assert_any_call("test-cluster", "default", "test-pod-1")
        analyzer.analyze_pod.assert_any_call("test-cluster", "default", "test-pod-2")

        # Check the result
        assert len(results) == 2
        assert results[0] == pod1_result
        assert results[1] == pod2_result

    def test_analyze_cpu(self, analyzer):
        """Test _analyze_cpu method."""
        # Create test data with container information
        cpu_df = pd.DataFrame({
            "timestamp": pd.date_range(start="2023-01-01", periods=10, freq="5min"),
            "cpu_usage_cores": [0.1, 0.2, 0.3, 0.4, 0.5, 0.4, 0.3, 0.2, 0.1, 0.2],
            "container": ["app"] * 10,  # Add container column
        })

        resources = {
            "cpu_request": 0.5,
            "cpu_limit": 1.0,
            "containers": {  # Add container-level resources
                "app": {
                    "cpu_request": 0.5,
                    "cpu_limit": 1.0,
                }
            }
        }

        result = analyzer._analyze_cpu(cpu_df, resources)

        # Check the result (values are now in millicores)
        assert result["stats"]["min"] == 100.0  # 0.1 cores = 100 millicores
        assert result["stats"]["max"] == 500.0  # 0.5 cores = 500 millicores
        assert result["stats"]["mean"] == 270.0  # 0.27 cores = 270 millicores
        assert result["stats"]["median"] == 250.0  # 0.25 cores = 250 millicores
        assert 450.0 <= result["stats"]["p95"] <= 500.0  # 0.45-0.5 cores = 450-500 millicores
        assert 490.0 <= result["stats"]["p99"] <= 500.0  # 0.49-0.5 cores = 490-500 millicores

        assert result["current"]["request"] == 500.0  # 0.5 cores = 500 millicores
        assert result["current"]["limit"] == 1000.0  # 1.0 cores = 1000 millicores

        # With 20% headroom
        assert 540.0 <= result["recommended"]["request"] <= 600.0  # 0.5 * 1.2 * 1000
        assert 600.0 <= result["recommended"]["limit"] <= 900.0  # 0.5 * 1.2 * 1000

        # Change percentages remain the same regardless of units
        assert 8.0 <= result["change"]["request"] <= 20.0
        assert -40.0 <= result["change"]["limit"] <= -10.0

    def test_analyze_memory(self, analyzer):
        """Test _analyze_memory method."""
        # Create test data with container information
        memory_df = pd.DataFrame({
            "timestamp": pd.date_range(start="2023-01-01", periods=10, freq="5min"),
            "memory_usage_bytes": [104857600, 157286400, 209715200, 262144000, 314572800,
                                  262144000, 209715200, 157286400, 104857600, 157286400],
            "memory_usage_mib": [100, 150, 200, 250, 300, 250, 200, 150, 100, 150],
            "container": ["app"] * 10,  # Add container column
        })

        resources = {
            "memory_request_mib": 200,
            "memory_limit_mib": 300,
            "containers": {  # Add container-level resources
                "app": {
                    "memory_request_mib": 200,
                    "memory_limit_mib": 300,
                }
            }
        }

        result = analyzer._analyze_memory(memory_df, resources)

        # Check the result
        assert result["stats"]["min_mib"] == 100
        assert result["stats"]["max_mib"] == 300
        assert result["stats"]["mean_mib"] == 185
        assert result["stats"]["median_mib"] == 175
        assert 275 <= result["stats"]["p95_mib"] <= 300
        assert 295 <= result["stats"]["p99_mib"] <= 300

        assert result["current"]["request_mib"] == 200
        assert result["current"]["limit_mib"] == 300

        # With 30% headroom
        assert 360 <= result["recommended"]["request_mib"] <= 390  # 300 * 1.3
        assert 390 <= result["recommended"]["limit_mib"] <= 450    # 300 * 1.3

        assert 80.0 <= result["change"]["request"] <= 95.0
        assert 30.0 <= result["change"]["limit"] <= 50.0

    def test_calculate_change(self, analyzer):
        """Test _calculate_change method."""
        assert analyzer._calculate_change(100, 120) == 20.0
        assert analyzer._calculate_change(100, 80) == -20.0
        assert analyzer._calculate_change(0, 100) == float('inf')
        assert analyzer._calculate_change(None, 100) is None

    def test_ml_recommendations(self):
        """Test ML-based recommendations."""
        # Create a config with ML enabled
        config = Config()
        config.use_ml = True
        config.cpu_headroom_percent = 20
        config.memory_headroom_percent = 30

        # Create analyzer with mocked predictor
        analyzer = ResourceAnalyzer(config)
        analyzer.predictor = MagicMock(spec=ResourcePredictor)

        # Mock the predictor methods
        analyzer.predictor.predict_cpu_resources.return_value = (600, 900)  # millicores
        analyzer.predictor.predict_memory_resources.return_value = (300, 400)  # MiB

        # Create test data
        cpu_df = pd.DataFrame({
            "timestamp": pd.date_range(start="2023-01-01", periods=10, freq="5min"),
            "cpu_usage_cores": [0.1, 0.2, 0.3, 0.4, 0.5, 0.4, 0.3, 0.2, 0.1, 0.2],
            "container": ["app"] * 10,
        })

        memory_df = pd.DataFrame({
            "timestamp": pd.date_range(start="2023-01-01", periods=10, freq="5min"),
            "memory_usage_bytes": [104857600, 157286400, 209715200, 262144000, 314572800,
                                  262144000, 209715200, 157286400, 104857600, 157286400],
            "memory_usage_mib": [100, 150, 200, 250, 300, 250, 200, 150, 100, 150],
            "container": ["app"] * 10,
        })

        resources = {
            "cpu_request": 0.5,  # Will be converted to 500 millicores
            "cpu_limit": 1.0,   # Will be converted to 1000 millicores
            "memory_request_bytes": 209715200,  # 200 MiB
            "memory_limit_bytes": 314572800,    # 300 MiB
            "memory_request_mib": 200,
            "memory_limit_mib": 300,
            "containers": {
                "app": {
                    "cpu_request": 0.5,
                    "cpu_limit": 1.0,
                    "memory_request_bytes": 209715200,
                    "memory_limit_bytes": 314572800,
                    "memory_request_mib": 200,
                    "memory_limit_mib": 300,
                }
            }
        }

        # Analyze CPU
        cpu_result = analyzer._analyze_cpu(cpu_df, resources)

        # Check that the predictor was called (may be called multiple times for container-level and pod-level)
        analyzer.predictor.predict_cpu_resources.assert_called_with(cpu_df, 500.0, 1000.0)

        # Check that the ML recommendations were used
        assert cpu_result["recommended"]["request"] == 600
        assert cpu_result["recommended"]["limit"] == 900

        # Analyze memory
        memory_result = analyzer._analyze_memory(memory_df, resources)

        # Check that the predictor was called (may be called multiple times for container-level and pod-level)
        analyzer.predictor.predict_memory_resources.assert_called_with(memory_df, 200, 300)

        # Check that the ML recommendations were used
        assert memory_result["recommended"]["request_mib"] == 300
        assert memory_result["recommended"]["limit_mib"] == 400
