"""
Tests for the config module.
"""
import os
import pytest
from unittest.mock import patch

from optimus.config import Config, get_config


class TestConfig:
    """Tests for the Config class."""

    def test_required_env_vars(self):
        """Test that required environment variables are checked."""
        # Test missing OPTIMUS_PROMETHEUS_URL
        with patch.dict(os.environ, {
            "OPTIMUS_AUTH_HEADER_VALUE": "test-value",
        }, clear=True):
            with pytest.raises(ValueError, match="OPTIMUS_PROMETHEUS_URL environment variable is required"):
                Config()

        # Test missing OPTIMUS_AUTH_HEADER_VALUE
        with patch.dict(os.environ, {
            "OPTIMUS_PROMETHEUS_URL": "http://prometheus:9090",
        }, clear=True):
            with pytest.raises(ValueError, match="OPTIMUS_AUTH_HEADER_VALUE environment variable is required"):
                Config()

    def test_default_values(self):
        """Test that default values are set correctly."""
        with patch.dict(os.environ, {
            "OPTIMUS_PROMETHEUS_URL": "http://prometheus:9090",
            "OPTIMUS_AUTH_HEADER_VALUE": "test-value",
        }, clear=True):
            config = Config()
            assert config.prometheus_url == "http://prometheus:9090"
            assert config.auth_header_name == "X-API-KEY"
            assert config.auth_header_value == "test-value"
            assert config.time_period_hours == 24
            assert config.namespace is None
            assert config.namespaces == []
            assert config.pod_label_selector == ""
            assert config.cpu_headroom_percent == 20.0
            assert config.memory_headroom_percent == 30.0
            assert config.output_format == "text"
            assert config.output_file is None
            assert config.ml_model_type == "xgboost"  # Default ML model type

    def test_custom_values(self):
        """Test that custom values are set correctly."""
        with patch.dict(os.environ, {
            "OPTIMUS_PROMETHEUS_URL": "http://prometheus:9090",
            "OPTIMUS_AUTH_HEADER_NAME": "custom-header",
            "OPTIMUS_AUTH_HEADER_VALUE": "test-value",
            "OPTIMUS_TIME_PERIOD_HOURS": "12",
            "OPTIMUS_NAMESPACE": "default,kube-system,monitoring",
            "OPTIMUS_POD_LABEL_SELECTOR": "app=myapp",
            "OPTIMUS_CPU_HEADROOM_PERCENT": "10",
            "OPTIMUS_MEMORY_HEADROOM_PERCENT": "15",
            "OPTIMUS_OUTPUT_FORMAT": "json",
            "OPTIMUS_OUTPUT_FILE": "/tmp/output.json",
            "OPTIMUS_ML_MODEL_TYPE": "prophet",
        }, clear=True):
            config = Config()
            assert config.prometheus_url == "http://prometheus:9090"
            assert config.auth_header_name == "custom-header"
            assert config.auth_header_value == "test-value"
            assert config.time_period_hours == 12
            assert config.namespace == "default"
            assert config.namespaces == ["default", "kube-system", "monitoring"]
            assert config.pod_label_selector == "app=myapp"
            assert config.cpu_headroom_percent == 10.0
            assert config.memory_headroom_percent == 15.0
            assert config.output_format == "json"
            assert config.output_file == "/tmp/output.json"
            assert config.ml_model_type == "prophet"  # Custom ML model type

    def test_get_auth_headers(self):
        """Test that auth headers are returned correctly."""
        with patch.dict(os.environ, {
            "OPTIMUS_PROMETHEUS_URL": "http://prometheus:9090",
            "OPTIMUS_AUTH_HEADER_NAME": "custom-header",
            "OPTIMUS_AUTH_HEADER_VALUE": "test-value",
        }, clear=True):
            config = Config()
            headers = config.get_auth_headers()
            assert headers == {"custom-header": "test-value"}

    def test_to_dict(self):
        """Test that config is converted to dict correctly."""
        with patch.dict(os.environ, {
            "OPTIMUS_PROMETHEUS_URL": "http://prometheus:9090",
            "OPTIMUS_AUTH_HEADER_VALUE": "test-value",
        }, clear=True):
            config = Config()
            config_dict = config.to_dict()
            assert config_dict["prometheus_url"] == "http://prometheus:9090"
            assert config_dict["auth_header_name"] == "X-API-KEY"
            assert config_dict["time_period_hours"] == 24
            assert config_dict["namespace"] is None
            assert config_dict["namespaces"] == []
            assert config_dict["pod_label_selector"] == ""
            assert config_dict["cpu_headroom_percent"] == 20.0
            assert config_dict["memory_headroom_percent"] == 30.0
            assert config_dict["output_format"] == "text"
            assert config_dict["output_file"] is None
            assert config_dict["ml_model_type"] == "xgboost"  # Default ML model type

    def test_get_config_singleton(self):
        """Test that get_config returns a singleton."""
        with patch.dict(os.environ, {
            "OPTIMUS_PROMETHEUS_URL": "http://prometheus:9090",
            "OPTIMUS_AUTH_HEADER_VALUE": "test-value",
        }, clear=True):
            config1 = get_config()
            config2 = get_config()
            assert config1 is config2
