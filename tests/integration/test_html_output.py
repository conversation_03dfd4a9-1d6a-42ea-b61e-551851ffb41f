"""
Integration test for HTML output.
"""
import os
import tempfile
import unittest
from unittest.mock import patch, MagicMock

from optimus.cli import main


class TestHTMLOutputIntegration(unittest.TestCase):
    """Integration test for HTML output."""

    @patch('optimus.analyzer.ResourceAnalyzer')
    @patch('optimus.cli.setup_logging')
    @patch('optimus.metrics.PrometheusClient')
    def test_html_output_generation(self, mock_prometheus, mock_setup_logging, mock_analyzer):
        """Test HTML output generation."""
        # Create a mock analyzer that returns sample results
        mock_analyzer_instance = MagicMock()
        mock_analyzer.return_value = mock_analyzer_instance

        # Sample results
        sample_results = [
            {
                "cluster": "test-cluster",
                "namespace": "default",
                "pod": "test-pod",
                "cpu": {
                    "stats": {
                        "min": 100,
                        "mean": 200,
                        "median": 150,
                        "p95": 300,
                        "p99": 350,
                        "max": 400
                    },
                    "current": {
                        "request": 500,
                        "limit": 1000
                    },
                    "recommended": {
                        "request": 400,
                        "limit": 800
                    },
                    "change": {
                        "request": -20.0,
                        "limit": -20.0
                    },
                    "ml_based": True
                },
                "memory": {
                    "stats": {
                        "min_mib": 100,
                        "mean_mib": 200,
                        "median_mib": 150,
                        "p95_mib": 300,
                        "p99_mib": 350,
                        "max_mib": 400
                    },
                    "current": {
                        "request_mib": 500,
                        "limit_mib": 1000
                    },
                    "recommended": {
                        "request_mib": 400,
                        "limit_mib": 800
                    },
                    "change": {
                        "request": -20.0,
                        "limit": -20.0
                    },
                    "ml_based": True
                }
            }
        ]
        mock_analyzer_instance.analyze_all_pods.return_value = sample_results

        # Create a temporary directory for the output
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a temporary file for the output
            output_file = os.path.join(temp_dir, "report.html")

            # Create a temporary directory for the templates
            template_dir = os.path.join(temp_dir, "templates")
            os.makedirs(template_dir)

            # Create a simple template file
            template_path = os.path.join(template_dir, "report_template.html")
            with open(template_path, "w") as f:
                f.write("<html><body>{{ pod_count }} pods</body></html>")

            # Patch the os.path.dirname to return our temp directory
            with patch('os.path.dirname', return_value=temp_dir):
                # Patch the environment variables
                env_vars = {
                    'OPTIMUS_PROMETHEUS_URL': 'http://prometheus:9090',
                    'OPTIMUS_AUTH_HEADER_VALUE': 'test-token'
                }
                with patch.dict('os.environ', env_vars):
                    # Patch the command-line arguments
                    with patch('sys.argv', ['optimus', '--output-format', 'html', '--output-file', output_file]):
                        # Instead of running main(), let's directly call format_output_html
                        from optimus.cli import format_output_html

                        # Generate HTML output
                        html_output = format_output_html(sample_results, None, True)

                        # Write the output to a file
                        with open(output_file, "w") as f:
                            f.write(html_output)

                        # Check that the output file was created
                        self.assertTrue(os.path.exists(output_file))

                        # Check that the output contains the expected content
                        with open(output_file, "r") as f:
                            content = f.read()
                            self.assertIn("1 pods", content)


if __name__ == "__main__":
    unittest.main()
