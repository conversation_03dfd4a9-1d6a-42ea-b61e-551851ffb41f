# Optimus

Kubernetes resource optimization tool based on Prometheus metrics.

## Overview

Optimus analyzes CPU and memory usage of Kubernetes pods over a configurable time period (default: 24 hours) and suggests optimal resource request and limit settings based on actual usage patterns.

## Features

- Queries Prometheus for Kubernetes pod metrics
- Analyzes CPU and memory usage patterns at both pod and container levels
- Compares actual usage with current resource requests and limits
- Uses XGBoost machine learning to predict optimal resource settings based on usage patterns
- Suggests optimal resource settings with configurable headroom
- Supports filtering by namespace and pod labels
- Works with human-readable pod names (ignores unique suffixes like `-abcd1234`)
- Aggregates metrics for all pods with the same base name
- Provides detailed container-level resource analysis and recommendations
- Outputs results in text, JSON, or YAML format
- Generates visual charts for CPU and memory usage analysis at both pod and container levels

## Installation

```bash
pip install optimus
```

## Configuration

Optimus is configured using environment variables:

| Environment Variable | Description | Default |
|----------------------|-------------|---------|
| `OPTIMUS_PROMETHEUS_URL` | Prometheus API URL | (required) |
| `OPTIMUS_AUTH_HEADER_NAME` | Authentication header name | `x-request-header` |
| `OPTIMUS_AUTH_HEADER_VALUE` | Authentication header value | (required) |
| `OPTIMUS_TIME_PERIOD_HOURS` | Time period for analysis in hours | `24` |
| `OPTIMUS_CLUSTER` | Kubernetes cluster to analyze | (all clusters) |
| `OPTIMUS_NAMESPACE` | Kubernetes namespace to analyze | (all namespaces) |
| `OPTIMUS_POD_LABEL_SELECTOR` | Kubernetes pod label selector | (all pods) |
| `OPTIMUS_CPU_HEADROOM_PERCENT` | CPU headroom percentage | `20` |
| `OPTIMUS_MEMORY_HEADROOM_PERCENT` | Memory headroom percentage | `30` |
| `OPTIMUS_USE_ML` | Use machine learning for recommendations | `true` |
| `OPTIMUS_EXTENDED_TIME_PERIOD_HOURS` | Extended time period for ML training in hours | `168` (7 days) |
| `OPTIMUS_OUTPUT_FORMAT` | Output format (text, json, yaml, html) | `text` |
| `OPTIMUS_OUTPUT_FILE` | Output file path | (stdout) |

## Usage

```bash
# Set required environment variables
export OPTIMUS_PROMETHEUS_URL="http://prometheus:9090"
export OPTIMUS_AUTH_HEADER_VALUE="your-auth-token"

# Run Optimus
optimus

# Analyze specific cluster
optimus --cluster production

# Analyze specific namespace
optimus --namespace default

# Analyze pods with specific labels
optimus --selector app=myapp

# Analyze for a different time period
optimus --time-period 12

# Disable machine learning recommendations
optimus --no-ml

# Use machine learning with a longer training period
optimus --use-ml --extended-time-period 336  # 14 days

# Generate an HTML report with charts
optimus --use-ml --generate-charts --output-format html --output-file report.html

# Output in JSON format
optimus --output-format json

# Output to file
optimus --output-file recommendations.txt

# Generate charts
optimus --generate-charts

# Generate charts in a custom directory
optimus --generate-charts --charts-dir /path/to/charts

# Complete example with multiple options
optimus --namespace default --selector app=myapp --time-period 24 --generate-charts --charts-dir ./my-charts --output-file recommendations.txt
```

## Command-line Options

```
usage: optimus [-h] [--verbose] [--cluster CLUSTER] [--namespace NAMESPACE] [--selector SELECTOR]
               [--time-period TIME_PERIOD] [--use-ml] [--no-ml] [--extended-time-period EXTENDED_TIME_PERIOD]
               [--output-format {text,json,yaml,html}] [--output-file OUTPUT_FILE] [--generate-charts]
               [--charts-dir CHARTS_DIR]

Kubernetes resource optimization tool

options:
  -h, --help            show this help message and exit
  --verbose, -v         Enable verbose logging
  --cluster CLUSTER, -c CLUSTER
                        Kubernetes cluster to analyze (overrides environment variable)
  --namespace NAMESPACE, -n NAMESPACE
                        Kubernetes namespace to analyze (overrides environment variable)
  --selector SELECTOR, -l SELECTOR
                        Kubernetes label selector (overrides environment variable)
  --time-period TIME_PERIOD, -t TIME_PERIOD
                        Time period in hours (overrides environment variable)
  --use-ml              Use machine learning for recommendations (overrides environment variable)
  --no-ml               Disable machine learning for recommendations
  --extended-time-period EXTENDED_TIME_PERIOD
                        Extended time period in hours for ML training (overrides environment variable)
  --output-format {text,json,yaml,html}, -o {text,json,yaml,html}
                        Output format (overrides environment variable)
  --output-file OUTPUT_FILE, -f OUTPUT_FILE
                        Output file (overrides environment variable)
  --generate-charts, -g Generate charts
  --charts-dir CHARTS_DIR, -d CHARTS_DIR
                        Directory to save charts (default: charts)
```

## Development

```bash
# Clone the repository
git clone https://github.com/yourusername/optimus.git
cd optimus

# Install development dependencies
pip install -e ".[test]"

# Run tests
pytest
```

## License

MIT