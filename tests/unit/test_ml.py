"""
Unit tests for the ML module.
"""
import unittest
from unittest.mock import <PERSON>Mock, patch
import pandas as pd
import numpy as np
from optimus.ml import ResourcePredictor
from optimus.config import Config


class TestResourcePredictor(unittest.TestCase):
    """Test the ResourcePredictor class."""

    def setUp(self):
        """Set up test fixtures."""
        self.config = Config()
        self.config.cpu_headroom_percent = 20
        self.config.memory_headroom_percent = 30
        self.config.ml_model_type = 'xgboost'  # Default to XGBoost for tests
        self.predictor = ResourcePredictor(self.config)

        # Create test data
        self.cpu_df = pd.DataFrame({
            "timestamp": pd.date_range(start="2023-01-01", periods=100, freq="5min"),
            "cpu_usage_cores": np.random.uniform(0.1, 0.5, 100),
            "container": ["app"] * 100,
        })

        self.memory_df = pd.DataFrame({
            "timestamp": pd.date_range(start="2023-01-01", periods=100, freq="5min"),
            "memory_usage_bytes": np.random.uniform(104857600, 314572800, 100),  # 100-300 MiB
            "memory_usage_mib": np.random.uniform(100, 300, 100),
            "container": ["app"] * 100,
        })

    def test_prepare_features(self):
        """Test feature preparation."""
        features = self.predictor._prepare_features(self.cpu_df)

        # Check that time-based features are created
        self.assertIn("hour", features.columns)
        self.assertIn("day_of_week", features.columns)
        self.assertIn("is_weekend", features.columns)

        # Check that container features are created
        self.assertIn("container_app", features.columns)

    def test_train_cpu_model_xgboost(self):
        """Test training CPU model with XGBoost."""
        # Ensure XGBoost is selected
        self.predictor.model_type = 'xgboost'

        # Mock the _train_xgboost_model method
        self.predictor._train_xgboost_model = MagicMock(return_value="mock_xgboost_model")

        # Train the model
        self.predictor.train_cpu_model(self.cpu_df)

        # Check that _train_xgboost_model was called with the right arguments
        self.predictor._train_xgboost_model.assert_called_once()

        # Check that the model was set
        self.assertEqual(self.predictor.cpu_model, "mock_xgboost_model")

    def test_train_cpu_model_prophet(self):
        """Test training CPU model with Prophet."""
        # Switch to Prophet
        self.predictor.model_type = 'prophet'

        # Mock the _train_prophet_model method
        self.predictor._train_prophet_model = MagicMock(return_value="mock_prophet_model")

        # Train the model
        self.predictor.train_cpu_model(self.cpu_df)

        # Check that _train_prophet_model was called with the right arguments
        self.predictor._train_prophet_model.assert_called_once()
        args, kwargs = self.predictor._train_prophet_model.call_args
        self.assertEqual(args[0].equals(self.cpu_df), True)
        self.assertEqual(args[1], 'cpu_usage_cores')
        self.assertEqual(kwargs['scale'], 1000)

        # Check that the model was set
        self.assertEqual(self.predictor.cpu_model, "mock_prophet_model")

    def test_train_memory_model_xgboost(self):
        """Test training memory model with XGBoost."""
        # Ensure XGBoost is selected
        self.predictor.model_type = 'xgboost'

        # Mock the _train_xgboost_model method
        self.predictor._train_xgboost_model = MagicMock(return_value="mock_xgboost_model")

        # Train the model
        self.predictor.train_memory_model(self.memory_df)

        # Check that _train_xgboost_model was called with the right arguments
        self.predictor._train_xgboost_model.assert_called_once()

        # Check that the model was set
        self.assertEqual(self.predictor.memory_model, "mock_xgboost_model")

    def test_train_memory_model_prophet(self):
        """Test training memory model with Prophet."""
        # Switch to Prophet
        self.predictor.model_type = 'prophet'

        # Mock the _train_prophet_model method
        self.predictor._train_prophet_model = MagicMock(return_value="mock_prophet_model")

        # Train the model
        self.predictor.train_memory_model(self.memory_df)

        # Check that _train_prophet_model was called with the right arguments
        self.predictor._train_prophet_model.assert_called_once()
        args, kwargs = self.predictor._train_prophet_model.call_args
        self.assertEqual(args[0].equals(self.memory_df), True)
        self.assertEqual(args[1], 'memory_usage_mib')

        # Check that the model was set
        self.assertEqual(self.predictor.memory_model, "mock_prophet_model")

    def test_predict_cpu_resources(self):
        """Test CPU resource prediction."""
        # Set model type to XGBoost
        self.predictor.model_type = 'xgboost'

        # Mock the model
        self.predictor.cpu_model = MagicMock()
        self.predictor.cpu_model.get_booster = MagicMock()
        self.predictor.cpu_model.get_booster().feature_names = ['feature1', 'feature2']
        self.predictor.cpu_model.predict = MagicMock(return_value=np.array([200, 300, 400]))  # Values in millicores

        # Make prediction
        request, limit = self.predictor.predict_cpu_resources(self.cpu_df, 500, 1000)

        # Check that the model was called
        self.predictor.cpu_model.predict.assert_called_once()

        # Check that the prediction is reasonable
        self.assertGreaterEqual(request, 100)  # At least 100 millicores
        self.assertGreaterEqual(limit, request)  # Limit >= request

    def test_predict_memory_resources(self):
        """Test memory resource prediction."""
        # Mock the model
        self.predictor.memory_model = MagicMock()
        self.predictor.memory_model.predict = MagicMock(return_value=np.array([150, 200, 250]))

        # Make prediction
        request, limit = self.predictor.predict_memory_resources(self.memory_df, 200, 400)

        # Check that the model was called
        self.predictor.memory_model.predict.assert_called_once()

        # Check that the prediction is reasonable
        self.assertGreaterEqual(request, 64)  # At least 64 MiB
        self.assertGreaterEqual(limit, request)  # Limit >= request

    def test_statistical_fallback_cpu(self):
        """Test statistical fallback for CPU prediction."""
        # Ensure model is None to force fallback
        self.predictor.cpu_model = None

        # Make prediction
        request, limit = self.predictor.predict_cpu_resources(self.cpu_df, 500, 1000)

        # Check that the prediction is reasonable
        self.assertGreaterEqual(request, 100)  # At least 100 millicores
        self.assertGreaterEqual(limit, request)  # Limit >= request

    def test_statistical_fallback_memory(self):
        """Test statistical fallback for memory prediction."""
        # Ensure model is None to force fallback
        self.predictor.memory_model = None

        # Make prediction
        request, limit = self.predictor.predict_memory_resources(self.memory_df, 200, 400)

        # Check that the prediction is reasonable
        self.assertGreaterEqual(request, 64)  # At least 64 MiB
        self.assertGreaterEqual(limit, request)  # Limit >= request

    def test_empty_data_cpu(self):
        """Test CPU prediction with empty data."""
        # Create empty DataFrame
        empty_df = pd.DataFrame(columns=["timestamp", "cpu_usage_cores", "container"])

        # Make prediction
        request, limit = self.predictor.predict_cpu_resources(empty_df, 500, 1000)

        # Check that the prediction returns the current values
        self.assertEqual(request, 500)
        self.assertEqual(limit, 1000)

    def test_empty_data_memory(self):
        """Test memory prediction with empty data."""
        # Create empty DataFrame
        empty_df = pd.DataFrame(columns=["timestamp", "memory_usage_mib", "container"])

        # Make prediction
        request, limit = self.predictor.predict_memory_resources(empty_df, 200, 400)

        # Check that the prediction returns the current values
        self.assertEqual(request, 200)
        self.assertEqual(limit, 400)


if __name__ == "__main__":
    unittest.main()
