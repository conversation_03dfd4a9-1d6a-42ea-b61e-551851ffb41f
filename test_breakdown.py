#!/usr/bin/env python
"""
Test script for the optimization breakdown section.
"""
import os
import datetime
from optimus.cli import format_output_html

# Sample results
results = [
    {
        "cluster": "test-cluster",
        "namespace": "default",
        "pod": "test-pod-1",
        "cpu": {
            "stats": {
                "min": 100,
                "mean": 200,
                "median": 150,
                "p95": 300,
                "p99": 350,
                "max": 400
            },
            "current": {
                "request": 500,
                "limit": 1000
            },
            "recommended": {
                "request": 400,
                "limit": 800
            },
            "change": {
                "request": -20.0,
                "limit": -20.0
            },
            "ml_based": True,
            "ml_model_type": "xgboost",
            "containers": {
                "app": {
                    "stats": {
                        "min": 100,
                        "mean": 200,
                        "median": 150,
                        "p95": 300,
                        "p99": 350,
                        "max": 400
                    },
                    "current": {
                        "request": 500,
                        "limit": 1000
                    },
                    "recommended": {
                        "request": 400,
                        "limit": 800
                    },
                    "change": {
                        "request": -20.0,
                        "limit": -20.0
                    },
                    "ml_based": True,
                    "ml_model_type": "xgboost"
                }
            }
        },
        "memory": {
            "stats": {
                "min_mib": 100,
                "mean_mib": 200,
                "median_mib": 150,
                "p95_mib": 300,
                "p99_mib": 350,
                "max_mib": 400
            },
            "current": {
                "request_mib": 500,
                "limit_mib": 1000
            },
            "recommended": {
                "request_mib": 400,
                "limit_mib": 800
            },
            "change": {
                "request": -20.0,
                "limit": -20.0
            },
            "ml_based": True,
            "ml_model_type": "xgboost",
            "containers": {
                "app": {
                    "stats": {
                        "min_mib": 100,
                        "mean_mib": 200,
                        "median_mib": 150,
                        "p95_mib": 300,
                        "p99_mib": 350,
                        "max_mib": 400
                    },
                    "current": {
                        "request_mib": 500,
                        "limit_mib": 1000
                    },
                    "recommended": {
                        "request_mib": 400,
                        "limit_mib": 800
                    },
                    "change": {
                        "request": -20.0,
                        "limit": -20.0
                    },
                    "ml_based": True,
                    "ml_model_type": "xgboost"
                }
            }
        }
    },
    {
        "cluster": "test-cluster",
        "namespace": "default",
        "pod": "test-pod-2",
        "cpu": {
            "stats": {
                "min": 150,
                "mean": 250,
                "median": 200,
                "p95": 350,
                "p99": 400,
                "max": 450
            },
            "current": {
                "request": 600,
                "limit": 1200
            },
            "recommended": {
                "request": 450,
                "limit": 900
            },
            "change": {
                "request": -25.0,
                "limit": -25.0
            },
            "ml_based": True,
            "ml_model_type": "prophet",
            "containers": {
                "app": {
                    "stats": {
                        "min": 150,
                        "mean": 250,
                        "median": 200,
                        "p95": 350,
                        "p99": 400,
                        "max": 450
                    },
                    "current": {
                        "request": 600,
                        "limit": 1200
                    },
                    "recommended": {
                        "request": 450,
                        "limit": 900
                    },
                    "change": {
                        "request": -25.0,
                        "limit": -25.0
                    },
                    "ml_based": True,
                    "ml_model_type": "prophet"
                }
            }
        },
        "memory": {
            "stats": {
                "min_mib": 150,
                "mean_mib": 250,
                "median_mib": 200,
                "p95_mib": 350,
                "p99_mib": 400,
                "max_mib": 450
            },
            "current": {
                "request_mib": 600,
                "limit_mib": 1200
            },
            "recommended": {
                "request_mib": 450,
                "limit_mib": 900
            },
            "change": {
                "request": -25.0,
                "limit": -25.0
            },
            "ml_based": True,
            "ml_model_type": "prophet",
            "containers": {
                "app": {
                    "stats": {
                        "min_mib": 150,
                        "mean_mib": 250,
                        "median_mib": 200,
                        "p95_mib": 350,
                        "p99_mib": 400,
                        "max_mib": 450
                    },
                    "current": {
                        "request_mib": 600,
                        "limit_mib": 1200
                    },
                    "recommended": {
                        "request_mib": 450,
                        "limit_mib": 900
                    },
                    "change": {
                        "request": -25.0,
                        "limit": -25.0
                    },
                    "ml_based": True,
                    "ml_model_type": "prophet"
                }
            }
        }
    }
]

# Sample chart paths
chart_paths = {
    "test-cluster_default_test-pod-1": {
        "cpu": "test-pod-1_cpu.png",
        "memory": "test-pod-1_memory.png",
        "comparison": "test-pod-1_comparison.png",
        "containers": {
            "app": {
                "cpu": "test-pod-1/containers/app_cpu_usage.png",
                "memory": "test-pod-1/containers/app_memory_usage.png",
                "comparison": "test-pod-1/containers/app_resource_comparison.png"
            }
        }
    },
    "test-cluster_default_test-pod-2": {
        "cpu": "test-pod-2_cpu.png",
        "memory": "test-pod-2_memory.png",
        "comparison": "test-pod-2_comparison.png",
        "containers": {
            "app": {
                "cpu": "test-pod-2/containers/app_cpu_usage.png",
                "memory": "test-pod-2/containers/app_memory_usage.png",
                "comparison": "test-pod-2/containers/app_resource_comparison.png"
            }
        }
    }
}

# Generate HTML output
html_output = format_output_html(results, chart_paths, True)

# Write the output to a file
with open("breakdown_test_report.html", "w") as f:
    f.write(html_output)

print(f"HTML report with breakdown section generated: {os.path.abspath('breakdown_test_report.html')}")
