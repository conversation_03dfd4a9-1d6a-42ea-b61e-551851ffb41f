"""
Unit tests for HTML output.
"""
import os
import tempfile
import unittest
from unittest.mock import patch, MagicMock

from optimus.cli import format_output_html


class TestHTMLOutput(unittest.TestCase):
    """Test HTML output functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.results = [
            {
                "cluster": "test-cluster",
                "namespace": "default",
                "pod": "test-pod",
                "cpu": {
                    "stats": {
                        "min": 100,
                        "mean": 200,
                        "median": 150,
                        "p95": 300,
                        "p99": 350,
                        "max": 400
                    },
                    "current": {
                        "request": 500,
                        "limit": 1000
                    },
                    "recommended": {
                        "request": 400,
                        "limit": 800
                    },
                    "change": {
                        "request": -20.0,
                        "limit": -20.0
                    },
                    "ml_based": True,
                    "containers": {
                        "app": {
                            "stats": {
                                "min": 100,
                                "mean": 200,
                                "median": 150,
                                "p95": 300,
                                "p99": 350,
                                "max": 400
                            },
                            "current": {
                                "request": 500,
                                "limit": 1000
                            },
                            "recommended": {
                                "request": 400,
                                "limit": 800
                            },
                            "change": {
                                "request": -20.0,
                                "limit": -20.0
                            },
                            "ml_based": True
                        }
                    }
                },
                "memory": {
                    "stats": {
                        "min_mib": 100,
                        "mean_mib": 200,
                        "median_mib": 150,
                        "p95_mib": 300,
                        "p99_mib": 350,
                        "max_mib": 400
                    },
                    "current": {
                        "request_mib": 500,
                        "limit_mib": 1000
                    },
                    "recommended": {
                        "request_mib": 400,
                        "limit_mib": 800
                    },
                    "change": {
                        "request": -20.0,
                        "limit": -20.0
                    },
                    "ml_based": True,
                    "containers": {
                        "app": {
                            "stats": {
                                "min_mib": 100,
                                "mean_mib": 200,
                                "median_mib": 150,
                                "p95_mib": 300,
                                "p99_mib": 350,
                                "max_mib": 400
                            },
                            "current": {
                                "request_mib": 500,
                                "limit_mib": 1000
                            },
                            "recommended": {
                                "request_mib": 400,
                                "limit_mib": 800
                            },
                            "change": {
                                "request": -20.0,
                                "limit": -20.0
                            },
                            "ml_based": True
                        }
                    }
                }
            }
        ]

        self.chart_paths = {
            "test-pod": {
                "cpu": "charts/test-pod_cpu.png",
                "memory": "charts/test-pod_memory.png",
                "comparison": "charts/test-pod_comparison.png"
            }
        }

    @patch('optimus.cli.Environment')
    def test_format_output_html(self, mock_env):
        """Test HTML output formatting."""
        # Mock the Jinja2 environment and template
        mock_template = MagicMock()
        mock_env.return_value.get_template.return_value = mock_template
        mock_template.render.return_value = "<html>Test HTML</html>"

        # Call the function
        html = format_output_html(self.results, self.chart_paths, True)

        # Check that the template was rendered with the correct arguments
        mock_template.render.assert_called_once()
        args = mock_template.render.call_args[1]

        # Check that the required arguments were passed
        self.assertEqual(args["results"], self.results)
        # The charts are now processed differently, so we don't check for exact equality
        self.assertEqual(args["pod_count"], 1)
        self.assertEqual(args["namespace_count"], 1)
        self.assertEqual(args["use_ml"], True)

        # Check that the output is correct
        self.assertEqual(html, "<html>Test HTML</html>")

    def test_html_output_integration(self):
        """Test HTML output integration with the CLI."""
        # Create a temporary directory for the template
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create the templates directory
            os.makedirs(os.path.join(temp_dir, "templates"))

            # Create a simple template file
            template_path = os.path.join(temp_dir, "templates", "report_template.html")
            with open(template_path, "w") as f:
                f.write("<html><body>{{ pod_count }} pods</body></html>")

            # Patch the os.path.dirname to return our temp directory
            with patch('os.path.dirname', return_value=temp_dir):
                # Call the function
                html = format_output_html(self.results, self.chart_paths, True)

                # Check that the output contains the expected content
                self.assertIn("1 pods", html)


if __name__ == "__main__":
    unittest.main()
