<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Optimus Resource Optimization Report</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Cost calculation functions -->
    <script type="text/javascript">
        // Constants for cost calculation
        const CPU_COST_PER_CORE = 20.0; // $20 per core
        const MEMORY_COST_PER_GB = 5.0; // $5 per GB

        // Calculate monthly CPU cost
        function calculateMonthlyCpuCost(millicores) {
            if (millicores === null || millicores === undefined) return 0;
            // Convert millicores to cores (divide by 1000) and multiply by cost per core
            return ((millicores / 1000) * CPU_COST_PER_CORE).toFixed(2);
        }

        // Calculate monthly memory cost
        function calculateMonthlyMemoryCost(mib) {
            if (mib === null || mib === undefined) return 0;
            // Convert MiB to GB (divide by 1024) and multiply by cost per GB
            return ((mib / 1024) * MEMORY_COST_PER_GB).toFixed(2);
        }

        // Calculate total monthly cost
        function calculateTotalMonthlyCost(cpuMillicores, memoryMib) {
            const cpuCost = parseFloat(calculateMonthlyCpuCost(cpuMillicores));
            const memoryCost = parseFloat(calculateMonthlyMemoryCost(memoryMib));
            return (cpuCost + memoryCost).toFixed(2);
        }

        // Calculate savings percentage
        function calculateSavingsPercentage(currentCost, recommendedCost) {
            if (currentCost <= 0) return 0;
            return (((currentCost - recommendedCost) / currentCost) * 100).toFixed(1);
        }
    </script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        h1 {
            margin: 0;
            font-size: 28px;
        }
        h2 {
            color: #2c3e50;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        h3 { /* General style for H3, also affects H5 in containers if not overridden */
            color: #3498db;
            margin-top: 20px;
        }
        .timestamp {
            font-size: 14px;
            color: #bdc3c7;
            margin-top: 10px;
        }
        .pod-section {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        .pod-section > h2.collapsible-header {
            cursor: pointer;
            position: relative;
        }
        .pod-section > h2.collapsible-header::after {
            content: '+'; /* Plus sign */
            color: #2c3e50;
            font-weight: bold;
            float: right;
            margin-left: 5px;
        }
        .pod-section > h2.collapsible-header.active::after {
            content: "-"; /* Minus sign */
        }
        .collapsible-content {
            display: none; /* Collapsed by default */
            overflow: hidden;
            padding-top: 10px;
        }
        .resource-section {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .chart-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin: 20px 0;
        }
        .chart {
            width: 48%;
            margin-bottom: 20px;
            background-color: white;
            border-radius: 5px;
            padding: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .chart img {
            width: 100%;
            height: auto;
            border-radius: 5px;
        }
        .comparison-chart {
            width: 100%;
            margin-bottom: 20px;
            background-color: white;
            border-radius: 5px;
            padding: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .comparison-chart img {
            width: 100%;
            height: auto;
            border-radius: 5px;
        }
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .stats-table th, .stats-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e1e1e1;
        }
        .stats-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .stats-table tr:hover {
            background-color: #f5f5f5;
        }
        .recommendation {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        .container-section {
            margin-left: 20px;
            margin-bottom: 30px;
            padding: 15px;
            border-left: 3px solid #ecf0f1;
            background-color: #fafafa;
            border-radius: 0 5px 5px 0;
        }
        .container-section > h5 { /* Base style for h5 in containers */
            color: #3498db; /* Match h3 color */
            margin-top: 0; /* Container section already has padding */
            margin-bottom: 15px; /* Default bottom margin */
        }
        .container-section > h5.container-collapsible-header {
            cursor: pointer;
            position: relative;
            margin-bottom: 0; /* Remove bottom margin when collapsible, content div will space */
        }
        .container-section > h5.container-collapsible-header::after {
            content: '+'; /* Plus sign */
            color: #3498db; /* Match h5 text color */
            font-weight: bold;
            float: right;
            margin-left: 5px;
            font-size: 1em; /* Relative to h5 font size */
        }
        .container-section > h5.container-collapsible-header.active::after {
            content: "-"; /* Minus sign */
        }
        .container-collapsible-content {
            display: none; /* Collapsed by default */
            overflow: hidden;
            padding-top: 15px; /* Space below the h5 header */
        }
        .container-section .chart-container {
            margin: 15px 0;
        }
        .container-section .chart {
            width: 48%;
            margin-bottom: 15px;
        }
        .container-section .comparison-chart {
            width: 100%;
            margin-bottom: 15px;
        }
        .positive-change {
            color: #e74c3c;
        }
        .negative-change {
            color: #27ae60;
        }
        .neutral-change {
            color: #7f8c8d;
        }
        .ml-badge {
            display: inline-block;
            background-color: #9b59b6;
            color: white;
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 10px;
            margin-left: 10px;
            vertical-align: middle;
        }
        .ml-badge.xgboost {
            background-color: #9b59b6;
        }
        .ml-badge.prophet {
            background-color: #3498db;
        }
        .summary {
            background-color: #f0f9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .cost-summary {
            background-color: #f9f0ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #9b59b6;
        }
        .cost-card {
            background-color: #fff;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: inline-block;
            width: 30%;
            margin-right: 1.5%;
            vertical-align: top;
        }
        .cost-card h3 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 16px;
        }
        .cost-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
            color: #2c3e50;
        }
        .cost-savings {
            color: #27ae60;
            font-weight: bold;
        }
        .cost-section {
            background-color: #f5f5f5;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        .cost-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .cost-table th, .cost-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e1e1e1;
        }
        .cost-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .cost-table .total-row {
            background-color: #f8f8f8;
            font-weight: bold;
        }
        .cost-table .total-row td {
            border-top: 2px solid #ddd;
        }

        /* Optimization Breakdown Styles */
        .optimization-breakdown {
            background-color: #1a1f2e;
            color: #fff;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }

        .breakdown-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .breakdown-header h2 {
            margin: 0;
            font-size: 18px;
            display: flex;
            align-items: center;
        }

        .breakdown-header h2 i {
            margin-right: 10px;
            font-size: 20px;
        }

        .breakdown-header a {
            color: #3498db;
            text-decoration: none;
            font-size: 14px;
        }

        .breakdown-row {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .breakdown-label {
            width: 120px;
            font-weight: bold;
            font-size: 14px;
            text-align: right;
            padding-right: 15px;
        }

        .breakdown-bar {
            flex-grow: 1;
            height: 40px;
            position: relative;
            border-radius: 4px;
            overflow: hidden;
        }

        .breakdown-value {
            width: 80px;
            text-align: right;
            font-weight: bold;
            font-size: 16px;
            padding-left: 15px;
        }

        .current-spend-bar {
            background-color: #4a90e2;
        }

        .optimal-spend-bar {
            background-color: #2ecc71;
        }

        .immediate-savings-bar {
            background-color: #f1c40f;
        }

        .risk-removal-bar {
            background-color: #e74c3c;
        }

        .additional-waste-bar {
            background-color: #f39c12;
        }

        .breakdown-metrics {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            gap: 30px;
        }

        .breakdown-metric {
            text-align: center;
            padding: 0 20px;
        }

        .breakdown-metric-label {
            font-size: 14px;
            margin-bottom: 5px;
        }

        .breakdown-metric-value {
            font-size: 18px;
            font-weight: bold;
        }

        .breakdown-metric-value.good {
            color: #2ecc71;
        }

        .breakdown-metric-value.warning {
            color: #f1c40f;
        }

        .breakdown-metric-value.danger {
            color: #e74c3c;
        }

        .cluster-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            flex-wrap: wrap;
            gap: 20px;
        }

        .stat-box {
            background-color: #2a3142;
            border-radius: 8px;
            padding: 15px;
            flex: 1;
            min-width: 150px;
            text-align: center;
        }

        .stat-box-label {
            font-size: 14px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stat-box-label i {
            margin-right: 8px;
        }

        .stat-box-value {
            font-size: 24px;
            font-weight: bold;
        }

        .resource-breakdown {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            gap: 20px;
        }

        .resource-column {
            flex: 1;
        }

        .resource-box {
            background-color: #2a3142;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .resource-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .resource-header i {
            margin-right: 10px;
            font-size: 18px;
        }

        .resource-title {
            font-size: 16px;
            font-weight: bold;
            margin: 0;
        }

        .resource-value {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .resource-amount {
            font-size: 18px;
            font-weight: bold;
        }

        .resource-cost {
            font-size: 14px;
            color: #bbb;
        }

        .resource-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            display: inline-block;
        }

        .indicator-info {
            background-color: #3498db;
        }

        .indicator-warning {
            background-color: #f1c40f;
        }

        .indicator-danger {
            background-color: #e74c3c;
        }

        .indicator-success {
            background-color: #2ecc71;
        }

        /* Image modal/lightbox styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            margin: auto;
            display: block;
            max-width: 90%;
            max-height: 90%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 2px solid #fff;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
        }

        .close-modal {
            position: absolute;
            top: 15px;
            right: 25px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            transition: 0.3s;
            z-index: 1001;
        }

        .close-modal:hover,
        .close-modal:focus {
            color: #bbb;
            text-decoration: none;
            cursor: pointer;
        }

        .chart img, .comparison-chart img {
            cursor: pointer;
            transition: transform 0.2s;
        }

        .chart img:hover, .comparison-chart img:hover {
            transform: scale(1.03);
        }
        footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            color: #7f8c8d;
            font-size: 14px;
        }
    </style>
</head>
<body>
<header>
    <h1>Optimus Resource Optimization Report</h1>
    <div class="timestamp">Generated on: 2025-05-20 11:17:28</div>
</header>

<div class="summary">
    <h2>Summary</h2>
    <p>This report provides resource optimization recommendations for 5 pods across 1 namespaces.</p>
</div>

<div class="cost-summary">
    <h2>Cost Analysis</h2>
    <p>Based on a pricing model of $20 per CPU core and $5 per GB of memory per month.</p>

    <div class="cost-card">
        <h3>Current Monthly Spend</h3>
        <div class="cost-value" id="total-current-cost">$0.00</div>
        <p>Total cost of current resource allocation</p>
    </div>

    <div class="cost-card">
        <h3>Recommended Monthly Spend</h3>
        <div class="cost-value" id="total-recommended-cost">$0.00</div>
        <p>Total cost with recommended resource allocation</p>
    </div>

    <div class="cost-card">
        <h3>Potential Monthly Savings</h3>
        <div class="cost-value cost-savings" id="total-savings">$0.00 (0%)</div>
        <p>Estimated savings with recommended resources</p>
    </div>
</div>

<!-- Optimization Breakdown -->
<div class="optimization-breakdown">
    <div class="breakdown-header">
        <h2><i class="fas fa-chart-pie"></i> Overall Monthly Spend <span style="font-size: 14px; font-weight: normal; margin-left: 5px;">(based on $20/CPU Core, $5/Memory GB)</span></h2>
        <a href="#detailed-cost-analysis">View details</a>
    </div>

    <!-- Current Spend Bar -->
    <div class="breakdown-row">
        <div class="breakdown-label">Current Spend</div>
        <div class="breakdown-bar current-spend-bar" id="current-spend-bar"></div>
        <div class="breakdown-value" id="current-spend-value">$0</div>
    </div>

    <!-- Optimal Spend Bar -->
    <div class="breakdown-row">
        <div class="breakdown-label">Optimal Spend</div>
        <div class="breakdown-bar optimal-spend-bar" id="optimal-spend-bar"></div>
        <div class="breakdown-value" id="optimal-spend-value">$0</div>
    </div>

    <!-- Metrics -->
    <div class="breakdown-metrics">
        <div class="breakdown-metric">
            <div class="breakdown-metric-label">Waste</div>
            <div class="breakdown-metric-value" id="waste-percent">0%</div>
        </div>
        <div class="breakdown-metric">
            <div class="breakdown-metric-label">Efficiency Rating</div>
            <div class="breakdown-metric-value" id="efficiency-rating">1</div>
        </div>
        <div class="breakdown-metric">
            <div class="breakdown-metric-label">Monthly Shortfall</div>
            <div class="breakdown-metric-value" id="monthly-shortfall">$0</div>
        </div>
    </div>

    <!-- Cluster Stats -->
    <div class="cluster-stats">
        <div class="stat-box">
            <div class="stat-box-label"><i class="fas fa-cube"></i> Avg no. of Containers</div>
            <div class="stat-box-value" id="container-count">0</div>
        </div>
        <div class="stat-box">
            <div class="stat-box-label"><i class="fas fa-server"></i> Pods</div>
            <div class="stat-box-value" id="pod-count">0</div>
        </div>
        <div class="stat-box">
            <div class="stat-box-label"><i class="fas fa-project-diagram"></i> Namespaces</div>
            <div class="stat-box-value" id="namespace-count">0</div>
        </div>
    </div>

    <!-- Resource Surplus/Deficit -->
    <div class="resource-breakdown">
        <div class="resource-column">
            <div class="resource-box">
                <div class="resource-header">
                    <span class="resource-indicator" id="cpu-indicator"></span>
                    <h3 class="resource-title">Surplus CPU Request</h3>
                </div>
                <div class="resource-value">
                    <span class="resource-amount" id="cpu-surplus">0</span>
                    <span class="resource-cost" id="cpu-surplus-cost">$0</span>
                </div>
            </div>
        </div>
        <div class="resource-column">
            <div class="resource-box">
                <div class="resource-header">
                    <span class="resource-indicator" id="memory-indicator"></span>
                    <h3 class="resource-title">Surplus Memory Request</h3>
                </div>
                <div class="resource-value">
                    <span class="resource-amount" id="memory-surplus">0</span>
                    <span class="resource-cost" id="memory-surplus-cost">$0</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Optimization Breakdown Details -->
<div class="optimization-breakdown" id="detailed-cost-analysis">
    <div class="breakdown-header">
        <h2><i class="fas fa-chart-bar"></i> Optimization Breakdown</h2>
    </div>

    <!-- Current Spend Bar -->
    <div class="breakdown-row">
        <div class="breakdown-label">Current Spend</div>
        <div class="breakdown-bar current-spend-bar" id="detailed-current-spend-bar"></div>
        <div class="breakdown-value" id="detailed-current-spend-value">$0</div>
    </div>

    <!-- Immediate Savings Bar -->
    <div class="breakdown-row">
        <div class="breakdown-label">Immediate Savings</div>
        <div class="breakdown-bar immediate-savings-bar" id="immediate-savings-bar"></div>
        <div class="breakdown-value" id="immediate-savings-value">$0</div>
    </div>

    <!-- Risk Removal Bar -->
    <div class="breakdown-row">
        <div class="breakdown-label">Risk Removal</div>
        <div class="breakdown-bar risk-removal-bar" id="risk-removal-bar"></div>
        <div class="breakdown-value" id="risk-removal-value">$0</div>
    </div>

    <!-- Additional Waste Bar -->
    <div class="breakdown-row">
        <div class="breakdown-label">Additional Waste</div>
        <div class="breakdown-bar additional-waste-bar" id="additional-waste-bar"></div>
        <div class="breakdown-value" id="additional-waste-value">$0</div>
    </div>

    <!-- Optimal Spend Bar -->
    <div class="breakdown-row">
        <div class="breakdown-label">Optimal Spend</div>
        <div class="breakdown-bar optimal-spend-bar" id="detailed-optimal-spend-bar"></div>
        <div class="breakdown-value" id="detailed-optimal-spend-value">$0</div>
    </div>

    <!-- Resource Breakdown -->
    <div class="resource-breakdown">
        <div class="resource-column">
            <h3 style="text-align: center; margin-bottom: 15px;">CPU Request</h3>

            <!-- Current CPU -->
            <div class="resource-box">
                <div class="resource-header">
                    <span class="resource-indicator indicator-info"></span>
                    <h3 class="resource-title">Current</h3>
                </div>
                <div class="resource-value">
                    <span class="resource-amount" id="current-cpu">0 Cores</span>
                    <span class="resource-cost" id="current-cpu-cost">$0</span>
                </div>
            </div>

            <!-- Immediate Savings CPU -->
            <div class="resource-box">
                <div class="resource-header">
                    <span class="resource-indicator indicator-warning"></span>
                    <h3 class="resource-title">Immediate Savings</h3>
                </div>
                <div class="resource-value">
                    <span class="resource-amount" id="immediate-cpu-savings">0 Cores</span>
                    <span class="resource-cost" id="immediate-cpu-savings-cost">$0</span>
                </div>
            </div>

            <!-- Risk Removal CPU -->
            <div class="resource-box">
                <div class="resource-header">
                    <span class="resource-indicator indicator-danger"></span>
                    <h3 class="resource-title">Risk Removal</h3>
                </div>
                <div class="resource-value">
                    <span class="resource-amount" id="risk-cpu">0 Cores</span>
                    <span class="resource-cost" id="risk-cpu-cost">$0</span>
                </div>
            </div>

            <!-- Additional Waste CPU -->
            <div class="resource-box">
                <div class="resource-header">
                    <span class="resource-indicator indicator-warning"></span>
                    <h3 class="resource-title">Additional Waste</h3>
                </div>
                <div class="resource-value">
                    <span class="resource-amount" id="waste-cpu">0 Cores</span>
                    <span class="resource-cost" id="waste-cpu-cost">$0</span>
                </div>
            </div>

            <!-- Optimal CPU -->
            <div class="resource-box">
                <div class="resource-header">
                    <span class="resource-indicator indicator-success"></span>
                    <h3 class="resource-title">Optimal</h3>
                </div>
                <div class="resource-value">
                    <span class="resource-amount" id="optimal-cpu">0 Cores</span>
                    <span class="resource-cost" id="optimal-cpu-cost">$0</span>
                </div>
            </div>
        </div>

        <div class="resource-column">
            <h3 style="text-align: center; margin-bottom: 15px;">Memory Request</h3>

            <!-- Current Memory -->
            <div class="resource-box">
                <div class="resource-header">
                    <span class="resource-indicator indicator-info"></span>
                    <h3 class="resource-title">Current</h3>
                </div>
                <div class="resource-value">
                    <span class="resource-amount" id="current-memory">0 GB</span>
                    <span class="resource-cost" id="current-memory-cost">$0</span>
                </div>
            </div>

            <!-- Immediate Savings Memory -->
            <div class="resource-box">
                <div class="resource-header">
                    <span class="resource-indicator indicator-warning"></span>
                    <h3 class="resource-title">Immediate Savings</h3>
                </div>
                <div class="resource-value">
                    <span class="resource-amount" id="immediate-memory-savings">0 GB</span>
                    <span class="resource-cost" id="immediate-memory-savings-cost">$0</span>
                </div>
            </div>

            <!-- Risk Removal Memory -->
            <div class="resource-box">
                <div class="resource-header">
                    <span class="resource-indicator indicator-danger"></span>
                    <h3 class="resource-title">Risk Removal</h3>
                </div>
                <div class="resource-value">
                    <span class="resource-amount" id="risk-memory">0 GB</span>
                    <span class="resource-cost" id="risk-memory-cost">$0</span>
                </div>
            </div>

            <!-- Additional Waste Memory -->
            <div class="resource-box">
                <div class="resource-header">
                    <span class="resource-indicator indicator-warning"></span>
                    <h3 class="resource-title">Additional Waste</h3>
                </div>
                <div class="resource-value">
                    <span class="resource-amount" id="waste-memory">0 GB</span>
                    <span class="resource-cost" id="waste-memory-cost">$0</span>
                </div>
            </div>

            <!-- Optimal Memory -->
            <div class="resource-box">
                <div class="resource-header">
                    <span class="resource-indicator indicator-success"></span>
                    <h3 class="resource-title">Optimal</h3>
                </div>
                <div class="resource-value">
                    <span class="resource-amount" id="optimal-memory">0 GB</span>
                    <span class="resource-cost" id="optimal-memory-cost">$0</span>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="pod-section">
    <h2 class="collapsible-header">monitoring/suse-observability-agent-logs-agent</h2>
    <div class="collapsible-content">
        <div class="chart-container">
            
            
            <div class="chart">
                <h3>CPU Usage Analysis</h3>
                <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-logs-agent/cpu_usage.png" alt="CPU Usage Analysis">
            </div>
            

            
            <div class="chart">
                <h3>Memory Usage Analysis</h3>
                <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-logs-agent/memory_usage.png" alt="Memory Usage Analysis">
            </div>
            

            
            <div class="comparison-chart">
                <h3>Resource Comparison</h3>
                <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-logs-agent/resource_comparison.png" alt="Resource Comparison">
            </div>
            
            
        </div>

        <!-- Pod Cost Analysis Section -->
        <div class="cost-section">
            <h3>Cost Analysis</h3>
            <p>Monthly cost estimates based on $20 per CPU core and $5 per GB of memory.</p>

            <table class="cost-table">
                <tr>
                    <th>Resource</th>
                    <th>Current Allocation</th>
                    <th>Current Monthly Cost</th>
                    <th>Recommended Allocation</th>
                    <th>Recommended Monthly Cost</th>
                    <th>Monthly Savings</th>
                </tr>
                <tr>
                    <td>CPU</td>
                    <td>20.0 millicores</td>
                    <td class="pod-cpu-current-cost" data-cpu="20.0">$0.00</td>
                    <td>40 millicores</td>
                    <td class="pod-cpu-recommended-cost" data-cpu="40">$0.00</td>
                    <td class="pod-cpu-savings">$0.00 (0%)</td>
                </tr>
                <tr>
                    <td>Memory</td>
                    <td>100.0 MiB</td>
                    <td class="pod-memory-current-cost" data-memory="100.0">$0.00</td>
                    <td>157 MiB</td>
                    <td class="pod-memory-recommended-cost" data-memory="157">$0.00</td>
                    <td class="pod-memory-savings">$0.00 (0%)</td>
                </tr>
                <tr class="total-row">
                    <td><strong>Total</strong></td>
                    <td></td>
                    <td class="pod-total-current-cost">$0.00</td>
                    <td></td>
                    <td class="pod-total-recommended-cost">$0.00</td>
                    <td class="pod-total-savings">$0.00 (0%)</td>
                </tr>
            </table>
        </div>

        
        <div class="resource-section">
            <h3>CPU Analysis <span class="ml-badge xgboost">Xgboost</span></h3>
            <h4>Pod-level Summary</h4>
            <table class="stats-table">
                <tr><th>Statistic</th><th>Value (millicores)</th></tr>
                <tr><td>Minimum</td><td>20.8579780986575</td></tr>
                <tr><td>Mean</td><td>23.82363914556799</td></tr>
                <tr><td>Median</td><td>23.790711723099083</td></tr>
                <tr><td>95th Percentile</td><td>25.550623462547648</td></tr>
                <tr><td>99th Percentile</td><td>25.94727847805315</td></tr>
                <tr><td>Maximum</td><td>27.708680853305268</td></tr>
            </table>
            <div class="recommendation">
                <h4>Resource Settings</h4>
                <table class="stats-table">
                    <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                    <tr>
                        <td>Request</td>
                        <td>20.0 millicores</td>
                        <td>40 millicores</td>
                        <td class="positive-change">
                            100.0%
                        </td>
                    </tr>
                    <tr>
                        <td>Limit</td>
                        <td>1300.0 millicores</td>
                        <td>48 millicores</td>
                        <td class="negative-change">
                            -96.3076923076923%
                        </td>
                    </tr>
                </table>
            </div>

            
            <h4>Container-level Analysis</h4>
            
            <div class="container-section">
                <h5 class="container-collapsible-header">logs-agent <span class="ml-badge xgboost">Xgboost</span></h5>
                <div class="container-collapsible-content"> {/* Content for CPU Container Section */}
                    
                    <div class="chart-container">
                        
                        <div class="chart">
                            <h3>CPU Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-logs-agent/containers/logs-agent_cpu_usage.png" alt="CPU Usage Analysis for logs-agent">
                        </div>
                        
                        
                        <div class="chart">
                            <h3>Memory Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-logs-agent/containers/logs-agent_memory_usage.png" alt="Memory Usage Analysis for logs-agent">
                        </div>
                        
                        
                        <div class="comparison-chart">
                            <h3>Resource Comparison</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-logs-agent/containers/logs-agent_resource_comparison.png" alt="Resource Comparison for logs-agent">
                        </div>
                        
                    </div>
                    
                    <table class="stats-table">
                        <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                        <tr>
                            <td>Request</td>
                            <td>20.0 millicores</td>
                            <td>40 millicores</td>
                            <td class="positive-change">
                                100.0%
                            </td>
                        </tr>
                        <tr>
                            <td>Limit</td>
                            <td>1300.0 millicores</td>
                            <td>48 millicores</td>
                            <td class="negative-change">
                                -96.3076923076923%
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            
        </div>
        

        
        <div class="resource-section">
            <h3>Memory Analysis <span class="ml-badge xgboost">Xgboost</span></h3>
            <h4>Pod-level Summary</h4>
            <table class="stats-table">
                <tr><th>Statistic</th><th>Value (MiB)</th></tr>
                <tr><td>Minimum</td><td>114.14453125</td></tr>
                <tr><td>Mean</td><td>117.77530276816609</td></tr>
                <tr><td>Median</td><td>117.517578125</td></tr>
                <tr><td>95th Percentile</td><td>120.76875</td></tr>
                <tr><td>99th Percentile</td><td>121.1725</td></tr>
                <tr><td>Maximum</td><td>121.611328125</td></tr>
            </table>
            <div class="recommendation">
                <h4>Resource Settings</h4>
                <table class="stats-table">
                    <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                    <tr>
                        <td>Request</td>
                        <td>100.0 MiB</td>
                        <td>157 MiB</td>
                        <td class="positive-change">
                            56.99999999999999%
                        </td>
                    </tr>
                    <tr>
                        <td>Limit</td>
                        <td>192.0 MiB</td>
                        <td>188 MiB</td>
                        <td class="negative-change">
                            -2.083333333333333%
                        </td>
                    </tr>
                </table>
            </div>

            
            <h4>Container-level Analysis</h4>
            
            <div class="container-section">
                <h5 class="container-collapsible-header">logs-agent <span class="ml-badge xgboost">Xgboost</span></h5>
                <div class="container-collapsible-content"> {/* Content for Memory Container Section */}
                    
                    <div class="chart-container">
                        
                        <div class="chart">
                            <h3>CPU Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-logs-agent/containers/logs-agent_cpu_usage.png" alt="CPU Usage Analysis for logs-agent">
                        </div>
                        
                        
                        <div class="chart">
                            <h3>Memory Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-logs-agent/containers/logs-agent_memory_usage.png" alt="Memory Usage Analysis for logs-agent">
                        </div>
                        
                        
                        <div class="comparison-chart">
                            <h3>Resource Comparison</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-logs-agent/containers/logs-agent_resource_comparison.png" alt="Resource Comparison for logs-agent">
                        </div>
                        
                    </div>
                    
                    <table class="stats-table">
                        <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                        <tr>
                            <td>Request</td>
                            <td>100.0 MiB</td>
                            <td>157 MiB</td>
                            <td class="positive-change">
                                56.99999999999999%
                            </td>
                        </tr>
                        <tr>
                            <td>Limit</td>
                            <td>192.0 MiB</td>
                            <td>188 MiB</td>
                            <td class="negative-change">
                                -2.083333333333333%
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            
        </div>
        
    </div> 
</div>

<div class="pod-section">
    <h2 class="collapsible-header">monitoring/suse-observability-agent-node-agent</h2>
    <div class="collapsible-content">
        <div class="chart-container">
            
            
            <div class="chart">
                <h3>CPU Usage Analysis</h3>
                <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-node-agent/cpu_usage.png" alt="CPU Usage Analysis">
            </div>
            

            
            <div class="chart">
                <h3>Memory Usage Analysis</h3>
                <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-node-agent/memory_usage.png" alt="Memory Usage Analysis">
            </div>
            

            
            <div class="comparison-chart">
                <h3>Resource Comparison</h3>
                <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-node-agent/resource_comparison.png" alt="Resource Comparison">
            </div>
            
            
        </div>

        <!-- Pod Cost Analysis Section -->
        <div class="cost-section">
            <h3>Cost Analysis</h3>
            <p>Monthly cost estimates based on $20 per CPU core and $5 per GB of memory.</p>

            <table class="cost-table">
                <tr>
                    <th>Resource</th>
                    <th>Current Allocation</th>
                    <th>Current Monthly Cost</th>
                    <th>Recommended Allocation</th>
                    <th>Recommended Monthly Cost</th>
                    <th>Monthly Savings</th>
                </tr>
                <tr>
                    <td>CPU</td>
                    <td>22.5 millicores</td>
                    <td class="pod-cpu-current-cost" data-cpu="22.5">$0.00</td>
                    <td>40 millicores</td>
                    <td class="pod-cpu-recommended-cost" data-cpu="40">$0.00</td>
                    <td class="pod-cpu-savings">$0.00 (0%)</td>
                </tr>
                <tr>
                    <td>Memory</td>
                    <td>154.0 MiB</td>
                    <td class="pod-memory-current-cost" data-memory="154.0">$0.00</td>
                    <td>443 MiB</td>
                    <td class="pod-memory-recommended-cost" data-memory="443">$0.00</td>
                    <td class="pod-memory-savings">$0.00 (0%)</td>
                </tr>
                <tr class="total-row">
                    <td><strong>Total</strong></td>
                    <td></td>
                    <td class="pod-total-current-cost">$0.00</td>
                    <td></td>
                    <td class="pod-total-recommended-cost">$0.00</td>
                    <td class="pod-total-savings">$0.00 (0%)</td>
                </tr>
            </table>
        </div>

        
        <div class="resource-section">
            <h3>CPU Analysis <span class="ml-badge xgboost">Xgboost</span></h3>
            <h4>Pod-level Summary</h4>
            <table class="stats-table">
                <tr><th>Statistic</th><th>Value (millicores)</th></tr>
                <tr><td>Minimum</td><td>15.868218131651027</td></tr>
                <tr><td>Mean</td><td>21.622833564067943</td></tr>
                <tr><td>Median</td><td>22.587562098500012</td></tr>
                <tr><td>95th Percentile</td><td>25.809979392328838</td></tr>
                <tr><td>99th Percentile</td><td>27.11341570470173</td></tr>
                <tr><td>Maximum</td><td>29.711037452198287</td></tr>
            </table>
            <div class="recommendation">
                <h4>Resource Settings</h4>
                <table class="stats-table">
                    <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                    <tr>
                        <td>Request</td>
                        <td>22.5 millicores</td>
                        <td>40 millicores</td>
                        <td class="positive-change">
                            77.77777777777779%
                        </td>
                    </tr>
                    <tr>
                        <td>Limit</td>
                        <td>197.5 millicores</td>
                        <td>48 millicores</td>
                        <td class="negative-change">
                            -75.69620253164557%
                        </td>
                    </tr>
                </table>
            </div>

            
            <h4>Container-level Analysis</h4>
            
            <div class="container-section">
                <h5 class="container-collapsible-header">node-agent <span class="ml-badge xgboost">Xgboost</span></h5>
                <div class="container-collapsible-content"> {/* Content for CPU Container Section */}
                    
                    <div class="chart-container">
                        
                        <div class="chart">
                            <h3>CPU Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-node-agent/containers/node-agent_cpu_usage.png" alt="CPU Usage Analysis for node-agent">
                        </div>
                        
                        
                        <div class="chart">
                            <h3>Memory Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-node-agent/containers/node-agent_memory_usage.png" alt="Memory Usage Analysis for node-agent">
                        </div>
                        
                        
                        <div class="comparison-chart">
                            <h3>Resource Comparison</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-node-agent/containers/node-agent_resource_comparison.png" alt="Resource Comparison for node-agent">
                        </div>
                        
                    </div>
                    
                    <table class="stats-table">
                        <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                        <tr>
                            <td>Request</td>
                            <td>20.0 millicores</td>
                            <td>40 millicores</td>
                            <td class="positive-change">
                                100.0%
                            </td>
                        </tr>
                        <tr>
                            <td>Limit</td>
                            <td>270.0 millicores</td>
                            <td>48 millicores</td>
                            <td class="negative-change">
                                -82.22222222222221%
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <div class="container-section">
                <h5 class="container-collapsible-header">process-agent <span class="ml-badge xgboost">Xgboost</span></h5>
                <div class="container-collapsible-content"> {/* Content for CPU Container Section */}
                    
                    <div class="chart-container">
                        
                        <div class="chart">
                            <h3>CPU Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-node-agent/containers/process-agent_cpu_usage.png" alt="CPU Usage Analysis for process-agent">
                        </div>
                        
                        
                        <div class="chart">
                            <h3>Memory Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-node-agent/containers/process-agent_memory_usage.png" alt="Memory Usage Analysis for process-agent">
                        </div>
                        
                        
                        <div class="comparison-chart">
                            <h3>Resource Comparison</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-node-agent/containers/process-agent_resource_comparison.png" alt="Resource Comparison for process-agent">
                        </div>
                        
                    </div>
                    
                    <table class="stats-table">
                        <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                        <tr>
                            <td>Request</td>
                            <td>25.0 millicores</td>
                            <td>40 millicores</td>
                            <td class="positive-change">
                                60.0%
                            </td>
                        </tr>
                        <tr>
                            <td>Limit</td>
                            <td>125.0 millicores</td>
                            <td>48 millicores</td>
                            <td class="negative-change">
                                -61.6%
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            
        </div>
        

        
        <div class="resource-section">
            <h3>Memory Analysis <span class="ml-badge xgboost">Xgboost</span></h3>
            <h4>Pod-level Summary</h4>
            <table class="stats-table">
                <tr><th>Statistic</th><th>Value (MiB)</th></tr>
                <tr><td>Minimum</td><td>159.8203125</td></tr>
                <tr><td>Mean</td><td>242.9011711991782</td></tr>
                <tr><td>Median</td><td>208.521484375</td></tr>
                <tr><td>95th Percentile</td><td>341.305078125</td></tr>
                <tr><td>99th Percentile</td><td>363.82662109375</td></tr>
                <tr><td>Maximum</td><td>365.814453125</td></tr>
            </table>
            <div class="recommendation">
                <h4>Resource Settings</h4>
                <table class="stats-table">
                    <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                    <tr>
                        <td>Request</td>
                        <td>154.0 MiB</td>
                        <td>443 MiB</td>
                        <td class="positive-change">
                            187.66233766233768%
                        </td>
                    </tr>
                    <tr>
                        <td>Limit</td>
                        <td>410.0 MiB</td>
                        <td>532 MiB</td>
                        <td class="positive-change">
                            29.756097560975608%
                        </td>
                    </tr>
                </table>
            </div>

            
            <h4>Container-level Analysis</h4>
            
            <div class="container-section">
                <h5 class="container-collapsible-header">node-agent <span class="ml-badge xgboost">Xgboost</span></h5>
                <div class="container-collapsible-content"> {/* Content for Memory Container Section */}
                    
                    <div class="chart-container">
                        
                        <div class="chart">
                            <h3>CPU Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-node-agent/containers/node-agent_cpu_usage.png" alt="CPU Usage Analysis for node-agent">
                        </div>
                        
                        
                        <div class="chart">
                            <h3>Memory Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-node-agent/containers/node-agent_memory_usage.png" alt="Memory Usage Analysis for node-agent">
                        </div>
                        
                        
                        <div class="comparison-chart">
                            <h3>Resource Comparison</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-node-agent/containers/node-agent_resource_comparison.png" alt="Resource Comparison for node-agent">
                        </div>
                        
                    </div>
                    
                    <table class="stats-table">
                        <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                        <tr>
                            <td>Request</td>
                            <td>180.0 MiB</td>
                            <td>215 MiB</td>
                            <td class="positive-change">
                                19.444444444444446%
                            </td>
                        </tr>
                        <tr>
                            <td>Limit</td>
                            <td>420.0 MiB</td>
                            <td>258 MiB</td>
                            <td class="negative-change">
                                -38.57142857142858%
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <div class="container-section">
                <h5 class="container-collapsible-header">process-agent <span class="ml-badge xgboost">Xgboost</span></h5>
                <div class="container-collapsible-content"> {/* Content for Memory Container Section */}
                    
                    <div class="chart-container">
                        
                        <div class="chart">
                            <h3>CPU Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-node-agent/containers/process-agent_cpu_usage.png" alt="CPU Usage Analysis for process-agent">
                        </div>
                        
                        
                        <div class="chart">
                            <h3>Memory Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-node-agent/containers/process-agent_memory_usage.png" alt="Memory Usage Analysis for process-agent">
                        </div>
                        
                        
                        <div class="comparison-chart">
                            <h3>Resource Comparison</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-node-agent/containers/process-agent_resource_comparison.png" alt="Resource Comparison for process-agent">
                        </div>
                        
                    </div>
                    
                    <table class="stats-table">
                        <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                        <tr>
                            <td>Request</td>
                            <td>128.0 MiB</td>
                            <td>444 MiB</td>
                            <td class="positive-change">
                                246.875%
                            </td>
                        </tr>
                        <tr>
                            <td>Limit</td>
                            <td>400.0 MiB</td>
                            <td>533 MiB</td>
                            <td class="positive-change">
                                33.25%
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            
        </div>
        
    </div> 
</div>

<div class="pod-section">
    <h2 class="collapsible-header">monitoring/suse-observability-agent-checks-agent-844d4884</h2>
    <div class="collapsible-content">
        <div class="chart-container">
            
            
            <div class="chart">
                <h3>CPU Usage Analysis</h3>
                <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-checks-agent-844d4884/cpu_usage.png" alt="CPU Usage Analysis">
            </div>
            

            
            <div class="chart">
                <h3>Memory Usage Analysis</h3>
                <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-checks-agent-844d4884/memory_usage.png" alt="Memory Usage Analysis">
            </div>
            

            
            <div class="comparison-chart">
                <h3>Resource Comparison</h3>
                <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-checks-agent-844d4884/resource_comparison.png" alt="Resource Comparison">
            </div>
            
            
        </div>

        <!-- Pod Cost Analysis Section -->
        <div class="cost-section">
            <h3>Cost Analysis</h3>
            <p>Monthly cost estimates based on $20 per CPU core and $5 per GB of memory.</p>

            <table class="cost-table">
                <tr>
                    <th>Resource</th>
                    <th>Current Allocation</th>
                    <th>Current Monthly Cost</th>
                    <th>Recommended Allocation</th>
                    <th>Recommended Monthly Cost</th>
                    <th>Monthly Savings</th>
                </tr>
                <tr>
                    <td>CPU</td>
                    <td>20.0 millicores</td>
                    <td class="pod-cpu-current-cost" data-cpu="20.0">$0.00</td>
                    <td>40 millicores</td>
                    <td class="pod-cpu-recommended-cost" data-cpu="40">$0.00</td>
                    <td class="pod-cpu-savings">$0.00 (0%)</td>
                </tr>
                <tr>
                    <td>Memory</td>
                    <td>512.0 MiB</td>
                    <td class="pod-memory-current-cost" data-memory="512.0">$0.00</td>
                    <td>103 MiB</td>
                    <td class="pod-memory-recommended-cost" data-memory="103">$0.00</td>
                    <td class="pod-memory-savings">$0.00 (0%)</td>
                </tr>
                <tr class="total-row">
                    <td><strong>Total</strong></td>
                    <td></td>
                    <td class="pod-total-current-cost">$0.00</td>
                    <td></td>
                    <td class="pod-total-recommended-cost">$0.00</td>
                    <td class="pod-total-savings">$0.00 (0%)</td>
                </tr>
            </table>
        </div>

        
        <div class="resource-section">
            <h3>CPU Analysis <span class="ml-badge xgboost">Xgboost</span></h3>
            <h4>Pod-level Summary</h4>
            <table class="stats-table">
                <tr><th>Statistic</th><th>Value (millicores)</th></tr>
                <tr><td>Minimum</td><td>0.813138079305</td></tr>
                <tr><td>Mean</td><td>1.0016415689628335</td></tr>
                <tr><td>Median</td><td>0.924456351562</td></tr>
                <tr><td>95th Percentile</td><td>1.31106729990815</td></tr>
                <tr><td>99th Percentile</td><td>1.3826784775009668</td></tr>
                <tr><td>Maximum</td><td>1.4152241971484898</td></tr>
            </table>
            <div class="recommendation">
                <h4>Resource Settings</h4>
                <table class="stats-table">
                    <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                    <tr>
                        <td>Request</td>
                        <td>20.0 millicores</td>
                        <td>40 millicores</td>
                        <td class="positive-change">
                            100.0%
                        </td>
                    </tr>
                    <tr>
                        <td>Limit</td>
                        <td>400.0 millicores</td>
                        <td>48 millicores</td>
                        <td class="negative-change">
                            -88.0%
                        </td>
                    </tr>
                </table>
            </div>

            
            <h4>Container-level Analysis</h4>
            
            <div class="container-section">
                <h5 class="container-collapsible-header">suse-observability-agent <span class="ml-badge xgboost">Xgboost</span></h5>
                <div class="container-collapsible-content"> {/* Content for CPU Container Section */}
                    
                    <div class="chart-container">
                        
                        <div class="chart">
                            <h3>CPU Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-checks-agent-844d4884/containers/suse-observability-agent_cpu_usage.png" alt="CPU Usage Analysis for suse-observability-agent">
                        </div>
                        
                        
                        <div class="chart">
                            <h3>Memory Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-checks-agent-844d4884/containers/suse-observability-agent_memory_usage.png" alt="Memory Usage Analysis for suse-observability-agent">
                        </div>
                        
                        
                        <div class="comparison-chart">
                            <h3>Resource Comparison</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-checks-agent-844d4884/containers/suse-observability-agent_resource_comparison.png" alt="Resource Comparison for suse-observability-agent">
                        </div>
                        
                    </div>
                    
                    <table class="stats-table">
                        <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                        <tr>
                            <td>Request</td>
                            <td>20.0 millicores</td>
                            <td>40 millicores</td>
                            <td class="positive-change">
                                100.0%
                            </td>
                        </tr>
                        <tr>
                            <td>Limit</td>
                            <td>400.0 millicores</td>
                            <td>48 millicores</td>
                            <td class="negative-change">
                                -88.0%
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            
        </div>
        

        
        <div class="resource-section">
            <h3>Memory Analysis <span class="ml-badge xgboost">Xgboost</span></h3>
            <h4>Pod-level Summary</h4>
            <table class="stats-table">
                <tr><th>Statistic</th><th>Value (MiB)</th></tr>
                <tr><td>Minimum</td><td>79.0546875</td></tr>
                <tr><td>Mean</td><td>79.20905871539793</td></tr>
                <tr><td>Median</td><td>79.31640625</td></tr>
                <tr><td>95th Percentile</td><td>79.3203125</td></tr>
                <tr><td>99th Percentile</td><td>79.32421875</td></tr>
                <tr><td>Maximum</td><td>79.32421875</td></tr>
            </table>
            <div class="recommendation">
                <h4>Resource Settings</h4>
                <table class="stats-table">
                    <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                    <tr>
                        <td>Request</td>
                        <td>512.0 MiB</td>
                        <td>103 MiB</td>
                        <td class="negative-change">
                            -79.8828125%
                        </td>
                    </tr>
                    <tr>
                        <td>Limit</td>
                        <td>600.0 MiB</td>
                        <td>124 MiB</td>
                        <td class="negative-change">
                            -79.33333333333333%
                        </td>
                    </tr>
                </table>
            </div>

            
            <h4>Container-level Analysis</h4>
            
            <div class="container-section">
                <h5 class="container-collapsible-header">suse-observability-agent <span class="ml-badge xgboost">Xgboost</span></h5>
                <div class="container-collapsible-content"> {/* Content for Memory Container Section */}
                    
                    <div class="chart-container">
                        
                        <div class="chart">
                            <h3>CPU Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-checks-agent-844d4884/containers/suse-observability-agent_cpu_usage.png" alt="CPU Usage Analysis for suse-observability-agent">
                        </div>
                        
                        
                        <div class="chart">
                            <h3>Memory Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-checks-agent-844d4884/containers/suse-observability-agent_memory_usage.png" alt="Memory Usage Analysis for suse-observability-agent">
                        </div>
                        
                        
                        <div class="comparison-chart">
                            <h3>Resource Comparison</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-checks-agent-844d4884/containers/suse-observability-agent_resource_comparison.png" alt="Resource Comparison for suse-observability-agent">
                        </div>
                        
                    </div>
                    
                    <table class="stats-table">
                        <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                        <tr>
                            <td>Request</td>
                            <td>512.0 MiB</td>
                            <td>103 MiB</td>
                            <td class="negative-change">
                                -79.8828125%
                            </td>
                        </tr>
                        <tr>
                            <td>Limit</td>
                            <td>600.0 MiB</td>
                            <td>124 MiB</td>
                            <td class="negative-change">
                                -79.33333333333333%
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            
        </div>
        
    </div> 
</div>

<div class="pod-section">
    <h2 class="collapsible-header">monitoring/suse-observability-agent-cluster-agent-785746b646</h2>
    <div class="collapsible-content">
        <div class="chart-container">
            
            
            <div class="chart">
                <h3>CPU Usage Analysis</h3>
                <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-cluster-agent-785746b646/cpu_usage.png" alt="CPU Usage Analysis">
            </div>
            

            
            <div class="chart">
                <h3>Memory Usage Analysis</h3>
                <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-cluster-agent-785746b646/memory_usage.png" alt="Memory Usage Analysis">
            </div>
            

            
            <div class="comparison-chart">
                <h3>Resource Comparison</h3>
                <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-cluster-agent-785746b646/resource_comparison.png" alt="Resource Comparison">
            </div>
            
            
        </div>

        <!-- Pod Cost Analysis Section -->
        <div class="cost-section">
            <h3>Cost Analysis</h3>
            <p>Monthly cost estimates based on $20 per CPU core and $5 per GB of memory.</p>

            <table class="cost-table">
                <tr>
                    <th>Resource</th>
                    <th>Current Allocation</th>
                    <th>Current Monthly Cost</th>
                    <th>Recommended Allocation</th>
                    <th>Recommended Monthly Cost</th>
                    <th>Monthly Savings</th>
                </tr>
                <tr>
                    <td>CPU</td>
                    <td>70.0 millicores</td>
                    <td class="pod-cpu-current-cost" data-cpu="70.0">$0.00</td>
                    <td>45 millicores</td>
                    <td class="pod-cpu-recommended-cost" data-cpu="45">$0.00</td>
                    <td class="pod-cpu-savings">$0.00 (0%)</td>
                </tr>
                <tr>
                    <td>Memory</td>
                    <td>512.0 MiB</td>
                    <td class="pod-memory-current-cost" data-memory="512.0">$0.00</td>
                    <td>176 MiB</td>
                    <td class="pod-memory-recommended-cost" data-memory="176">$0.00</td>
                    <td class="pod-memory-savings">$0.00 (0%)</td>
                </tr>
                <tr class="total-row">
                    <td><strong>Total</strong></td>
                    <td></td>
                    <td class="pod-total-current-cost">$0.00</td>
                    <td></td>
                    <td class="pod-total-recommended-cost">$0.00</td>
                    <td class="pod-total-savings">$0.00 (0%)</td>
                </tr>
            </table>
        </div>

        
        <div class="resource-section">
            <h3>CPU Analysis <span class="ml-badge xgboost">Xgboost</span></h3>
            <h4>Pod-level Summary</h4>
            <table class="stats-table">
                <tr><th>Statistic</th><th>Value (millicores)</th></tr>
                <tr><td>Minimum</td><td>5.44384909360232</td></tr>
                <tr><td>Mean</td><td>28.02075916870814</td></tr>
                <tr><td>Median</td><td>9.606817432134914</td></tr>
                <tr><td>95th Percentile</td><td>55.65408799950442</td></tr>
                <tr><td>99th Percentile</td><td>63.121384234276434</td></tr>
                <tr><td>Maximum</td><td>65.1496052174472</td></tr>
            </table>
            <div class="recommendation">
                <h4>Resource Settings</h4>
                <table class="stats-table">
                    <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                    <tr>
                        <td>Request</td>
                        <td>70.0 millicores</td>
                        <td>45 millicores</td>
                        <td class="negative-change">
                            -35.714285714285715%
                        </td>
                    </tr>
                    <tr>
                        <td>Limit</td>
                        <td>400.0 millicores</td>
                        <td>67 millicores</td>
                        <td class="negative-change">
                            -83.25%
                        </td>
                    </tr>
                </table>
            </div>

            
            <h4>Container-level Analysis</h4>
            
            <div class="container-section">
                <h5 class="container-collapsible-header">cluster-agent <span class="ml-badge xgboost">Xgboost</span></h5>
                <div class="container-collapsible-content"> {/* Content for CPU Container Section */}
                    
                    <div class="chart-container">
                        
                        <div class="chart">
                            <h3>CPU Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-cluster-agent-785746b646/containers/cluster-agent_cpu_usage.png" alt="CPU Usage Analysis for cluster-agent">
                        </div>
                        
                        
                        <div class="chart">
                            <h3>Memory Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-cluster-agent-785746b646/containers/cluster-agent_memory_usage.png" alt="Memory Usage Analysis for cluster-agent">
                        </div>
                        
                        
                        <div class="comparison-chart">
                            <h3>Resource Comparison</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-cluster-agent-785746b646/containers/cluster-agent_resource_comparison.png" alt="Resource Comparison for cluster-agent">
                        </div>
                        
                    </div>
                    
                    <table class="stats-table">
                        <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                        <tr>
                            <td>Request</td>
                            <td>70.0 millicores</td>
                            <td>45 millicores</td>
                            <td class="negative-change">
                                -35.714285714285715%
                            </td>
                        </tr>
                        <tr>
                            <td>Limit</td>
                            <td>400.0 millicores</td>
                            <td>67 millicores</td>
                            <td class="negative-change">
                                -83.25%
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            
        </div>
        

        
        <div class="resource-section">
            <h3>Memory Analysis <span class="ml-badge xgboost">Xgboost</span></h3>
            <h4>Pod-level Summary</h4>
            <table class="stats-table">
                <tr><th>Statistic</th><th>Value (MiB)</th></tr>
                <tr><td>Minimum</td><td>101.296875</td></tr>
                <tr><td>Mean</td><td>127.49922956314879</td></tr>
                <tr><td>Median</td><td>123.1796875</td></tr>
                <tr><td>95th Percentile</td><td>153.29140625</td></tr>
                <tr><td>99th Percentile</td><td>157.81765625</td></tr>
                <tr><td>Maximum</td><td>161.16015625</td></tr>
            </table>
            <div class="recommendation">
                <h4>Resource Settings</h4>
                <table class="stats-table">
                    <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                    <tr>
                        <td>Request</td>
                        <td>512.0 MiB</td>
                        <td>176 MiB</td>
                        <td class="negative-change">
                            -65.625%
                        </td>
                    </tr>
                    <tr>
                        <td>Limit</td>
                        <td>800.0 MiB</td>
                        <td>211 MiB</td>
                        <td class="negative-change">
                            -73.625%
                        </td>
                    </tr>
                </table>
            </div>

            
            <h4>Container-level Analysis</h4>
            
            <div class="container-section">
                <h5 class="container-collapsible-header">cluster-agent <span class="ml-badge xgboost">Xgboost</span></h5>
                <div class="container-collapsible-content"> {/* Content for Memory Container Section */}
                    
                    <div class="chart-container">
                        
                        <div class="chart">
                            <h3>CPU Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-cluster-agent-785746b646/containers/cluster-agent_cpu_usage.png" alt="CPU Usage Analysis for cluster-agent">
                        </div>
                        
                        
                        <div class="chart">
                            <h3>Memory Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-cluster-agent-785746b646/containers/cluster-agent_memory_usage.png" alt="Memory Usage Analysis for cluster-agent">
                        </div>
                        
                        
                        <div class="comparison-chart">
                            <h3>Resource Comparison</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-observability-agent-cluster-agent-785746b646/containers/cluster-agent_resource_comparison.png" alt="Resource Comparison for cluster-agent">
                        </div>
                        
                    </div>
                    
                    <table class="stats-table">
                        <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                        <tr>
                            <td>Request</td>
                            <td>512.0 MiB</td>
                            <td>176 MiB</td>
                            <td class="negative-change">
                                -65.625%
                            </td>
                        </tr>
                        <tr>
                            <td>Limit</td>
                            <td>800.0 MiB</td>
                            <td>211 MiB</td>
                            <td class="negative-change">
                                -73.625%
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            
        </div>
        
    </div> 
</div>

<div class="pod-section">
    <h2 class="collapsible-header">monitoring/suse-opentelemetry-collector-85bbdc9f98</h2>
    <div class="collapsible-content">
        <div class="chart-container">
            
            
            <div class="chart">
                <h3>CPU Usage Analysis</h3>
                <img src="charts/dsc-suse-ai_monitoring_suse-opentelemetry-collector-85bbdc9f98/cpu_usage.png" alt="CPU Usage Analysis">
            </div>
            

            
            <div class="chart">
                <h3>Memory Usage Analysis</h3>
                <img src="charts/dsc-suse-ai_monitoring_suse-opentelemetry-collector-85bbdc9f98/memory_usage.png" alt="Memory Usage Analysis">
            </div>
            

            
            <div class="comparison-chart">
                <h3>Resource Comparison</h3>
                <img src="charts/dsc-suse-ai_monitoring_suse-opentelemetry-collector-85bbdc9f98/resource_comparison.png" alt="Resource Comparison">
            </div>
            
            
        </div>

        <!-- Pod Cost Analysis Section -->
        <div class="cost-section">
            <h3>Cost Analysis</h3>
            <p>Monthly cost estimates based on $20 per CPU core and $5 per GB of memory.</p>

            <table class="cost-table">
                <tr>
                    <th>Resource</th>
                    <th>Current Allocation</th>
                    <th>Current Monthly Cost</th>
                    <th>Recommended Allocation</th>
                    <th>Recommended Monthly Cost</th>
                    <th>Monthly Savings</th>
                </tr>
                <tr>
                    <td>CPU</td>
                    <td>0 millicores</td>
                    <td class="pod-cpu-current-cost" data-cpu="0">$0.00</td>
                    <td>40 millicores</td>
                    <td class="pod-cpu-recommended-cost" data-cpu="40">$0.00</td>
                    <td class="pod-cpu-savings">$0.00 (0%)</td>
                </tr>
                <tr>
                    <td>Memory</td>
                    <td>0 MiB</td>
                    <td class="pod-memory-current-cost" data-memory="0">$0.00</td>
                    <td>95 MiB</td>
                    <td class="pod-memory-recommended-cost" data-memory="95">$0.00</td>
                    <td class="pod-memory-savings">$0.00 (0%)</td>
                </tr>
                <tr class="total-row">
                    <td><strong>Total</strong></td>
                    <td></td>
                    <td class="pod-total-current-cost">$0.00</td>
                    <td></td>
                    <td class="pod-total-recommended-cost">$0.00</td>
                    <td class="pod-total-savings">$0.00 (0%)</td>
                </tr>
            </table>
        </div>

        
        <div class="resource-section">
            <h3>CPU Analysis <span class="ml-badge xgboost">Xgboost</span></h3>
            <h4>Pod-level Summary</h4>
            <table class="stats-table">
                <tr><th>Statistic</th><th>Value (millicores)</th></tr>
                <tr><td>Minimum</td><td>1.4861749006876674</td></tr>
                <tr><td>Mean</td><td>2.3029561282568216</td></tr>
                <tr><td>Median</td><td>2.1928484302817344</td></tr>
                <tr><td>95th Percentile</td><td>2.8501374916700635</td></tr>
                <tr><td>99th Percentile</td><td>2.982647333282252</td></tr>
                <tr><td>Maximum</td><td>3.0824621909267798</td></tr>
            </table>
            <div class="recommendation">
                <h4>Resource Settings</h4>
                <table class="stats-table">
                    <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                    <tr>
                        <td>Request</td>
                        <td>Not set millicores</td>
                        <td>40 millicores</td>
                        <td class="neutral-change">
                            N/A
                        </td>
                    </tr>
                    <tr>
                        <td>Limit</td>
                        <td>Not set millicores</td>
                        <td>48 millicores</td>
                        <td class="neutral-change">
                            N/A
                        </td>
                    </tr>
                </table>
            </div>

            
            <h4>Container-level Analysis</h4>
            
            <div class="container-section">
                <h5 class="container-collapsible-header">opentelemetry-collector <span class="ml-badge xgboost">Xgboost</span></h5>
                <div class="container-collapsible-content"> {/* Content for CPU Container Section */}
                    
                    <div class="chart-container">
                        
                        <div class="chart">
                            <h3>CPU Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-opentelemetry-collector-85bbdc9f98/containers/opentelemetry-collector_cpu_usage.png" alt="CPU Usage Analysis for opentelemetry-collector">
                        </div>
                        
                        
                        <div class="chart">
                            <h3>Memory Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-opentelemetry-collector-85bbdc9f98/containers/opentelemetry-collector_memory_usage.png" alt="Memory Usage Analysis for opentelemetry-collector">
                        </div>
                        
                        
                        <div class="comparison-chart">
                            <h3>Resource Comparison</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-opentelemetry-collector-85bbdc9f98/containers/opentelemetry-collector_resource_comparison.png" alt="Resource Comparison for opentelemetry-collector">
                        </div>
                        
                    </div>
                    
                    <table class="stats-table">
                        <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                        <tr>
                            <td>Request</td>
                            <td>Not set millicores</td>
                            <td>40 millicores</td>
                            <td class="neutral-change">
                                N/A
                            </td>
                        </tr>
                        <tr>
                            <td>Limit</td>
                            <td>Not set millicores</td>
                            <td>48 millicores</td>
                            <td class="neutral-change">
                                N/A
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            
        </div>
        

        
        <div class="resource-section">
            <h3>Memory Analysis <span class="ml-badge xgboost">Xgboost</span></h3>
            <h4>Pod-level Summary</h4>
            <table class="stats-table">
                <tr><th>Statistic</th><th>Value (MiB)</th></tr>
                <tr><td>Minimum</td><td>69.8125</td></tr>
                <tr><td>Mean</td><td>72.15133001730104</td></tr>
                <tr><td>Median</td><td>72.0</td></tr>
                <tr><td>95th Percentile</td><td>73.95703125</td></tr>
                <tr><td>99th Percentile</td><td>74.313125</td></tr>
                <tr><td>Maximum</td><td>74.79296875</td></tr>
            </table>
            <div class="recommendation">
                <h4>Resource Settings</h4>
                <table class="stats-table">
                    <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                    <tr>
                        <td>Request</td>
                        <td>Not set MiB</td>
                        <td>95 MiB</td>
                        <td class="neutral-change">
                            N/A
                        </td>
                    </tr>
                    <tr>
                        <td>Limit</td>
                        <td>Not set MiB</td>
                        <td>114 MiB</td>
                        <td class="neutral-change">
                            N/A
                        </td>
                    </tr>
                </table>
            </div>

            
            <h4>Container-level Analysis</h4>
            
            <div class="container-section">
                <h5 class="container-collapsible-header">opentelemetry-collector <span class="ml-badge xgboost">Xgboost</span></h5>
                <div class="container-collapsible-content"> {/* Content for Memory Container Section */}
                    
                    <div class="chart-container">
                        
                        <div class="chart">
                            <h3>CPU Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-opentelemetry-collector-85bbdc9f98/containers/opentelemetry-collector_cpu_usage.png" alt="CPU Usage Analysis for opentelemetry-collector">
                        </div>
                        
                        
                        <div class="chart">
                            <h3>Memory Usage Analysis</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-opentelemetry-collector-85bbdc9f98/containers/opentelemetry-collector_memory_usage.png" alt="Memory Usage Analysis for opentelemetry-collector">
                        </div>
                        
                        
                        <div class="comparison-chart">
                            <h3>Resource Comparison</h3>
                            <img src="charts/dsc-suse-ai_monitoring_suse-opentelemetry-collector-85bbdc9f98/containers/opentelemetry-collector_resource_comparison.png" alt="Resource Comparison for opentelemetry-collector">
                        </div>
                        
                    </div>
                    
                    <table class="stats-table">
                        <tr><th>Setting</th><th>Current</th><th>Recommended</th><th>Change</th></tr>
                        <tr>
                            <td>Request</td>
                            <td>Not set MiB</td>
                            <td>95 MiB</td>
                            <td class="neutral-change">
                                N/A
                            </td>
                        </tr>
                        <tr>
                            <td>Limit</td>
                            <td>Not set MiB</td>
                            <td>114 MiB</td>
                            <td class="neutral-change">
                                N/A
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            
        </div>
        
    </div> 
</div>


<!-- Image Modal/Lightbox -->
<div id="imageModal" class="modal">
    <span class="close-modal">&times;</span>
    <img class="modal-content" id="modalImage">
</div>

<footer>
    <p>Generated by Optimus Resource Optimizer</p>
</footer>

<script type="text/javascript">
    // Calculate costs when the page loads
    document.addEventListener('DOMContentLoaded', function() {
        calculateAllCosts();
    });

    // Calculate costs for all pods
    function calculateAllCosts() {
        let totalCurrentCost = 0;
        let totalRecommendedCost = 0;

        // Process each pod
        document.querySelectorAll('.pod-section').forEach(function(podSection) {
            // Calculate CPU costs
            const cpuCurrentElements = podSection.querySelectorAll('.pod-cpu-current-cost');
            const cpuRecommendedElements = podSection.querySelectorAll('.pod-cpu-recommended-cost');
            const cpuSavingsElements = podSection.querySelectorAll('.pod-cpu-savings');

            cpuCurrentElements.forEach(function(element, index) {
                const cpuMillicores = parseFloat(element.getAttribute('data-cpu') || 0);
                const cpuCurrentCost = parseFloat(calculateMonthlyCpuCost(cpuMillicores));
                element.textContent = '$' + cpuCurrentCost.toFixed(2);

                const recommendedElement = cpuRecommendedElements[index];
                const recommendedCpuMillicores = parseFloat(recommendedElement.getAttribute('data-cpu') || 0);
                const cpuRecommendedCost = parseFloat(calculateMonthlyCpuCost(recommendedCpuMillicores));
                recommendedElement.textContent = '$' + cpuRecommendedCost.toFixed(2);

                const cpuSavings = cpuCurrentCost - cpuRecommendedCost;
                const cpuSavingsPercent = calculateSavingsPercentage(cpuCurrentCost, cpuRecommendedCost);
                cpuSavingsElements[index].textContent = '$' + cpuSavings.toFixed(2) + ' (' + cpuSavingsPercent + '%)';
                if (cpuSavings > 0) {
                    cpuSavingsElements[index].classList.add('cost-savings');
                }
            });

            // Calculate Memory costs
            const memoryCurrentElements = podSection.querySelectorAll('.pod-memory-current-cost');
            const memoryRecommendedElements = podSection.querySelectorAll('.pod-memory-recommended-cost');
            const memorySavingsElements = podSection.querySelectorAll('.pod-memory-savings');

            memoryCurrentElements.forEach(function(element, index) {
                const memoryMiB = parseFloat(element.getAttribute('data-memory') || 0);
                const memoryCurrentCost = parseFloat(calculateMonthlyMemoryCost(memoryMiB));
                element.textContent = '$' + memoryCurrentCost.toFixed(2);

                const recommendedElement = memoryRecommendedElements[index];
                const recommendedMemoryMiB = parseFloat(recommendedElement.getAttribute('data-memory') || 0);
                const memoryRecommendedCost = parseFloat(calculateMonthlyMemoryCost(recommendedMemoryMiB));
                recommendedElement.textContent = '$' + memoryRecommendedCost.toFixed(2);

                const memorySavings = memoryCurrentCost - memoryRecommendedCost;
                const memorySavingsPercent = calculateSavingsPercentage(memoryCurrentCost, memoryRecommendedCost);
                memorySavingsElements[index].textContent = '$' + memorySavings.toFixed(2) + ' (' + memorySavingsPercent + '%)';
                if (memorySavings > 0) {
                    memorySavingsElements[index].classList.add('cost-savings');
                }
            });

            // Calculate pod totals
            const podTotalCurrentElements = podSection.querySelectorAll('.pod-total-current-cost');
            const podTotalRecommendedElements = podSection.querySelectorAll('.pod-total-recommended-cost');
            const podTotalSavingsElements = podSection.querySelectorAll('.pod-total-savings');

            podTotalCurrentElements.forEach(function(element, index) {
                const cpuCurrentCost = parseFloat(cpuCurrentElements[index]?.textContent.replace('$', '') || 0);
                const memoryCurrentCost = parseFloat(memoryCurrentElements[index]?.textContent.replace('$', '') || 0);
                const podTotalCurrent = cpuCurrentCost + memoryCurrentCost;
                element.textContent = '$' + podTotalCurrent.toFixed(2);

                const cpuRecommendedCost = parseFloat(cpuRecommendedElements[index]?.textContent.replace('$', '') || 0);
                const memoryRecommendedCost = parseFloat(memoryRecommendedElements[index]?.textContent.replace('$', '') || 0);
                const podTotalRecommended = cpuRecommendedCost + memoryRecommendedCost;
                podTotalRecommendedElements[index].textContent = '$' + podTotalRecommended.toFixed(2);

                const podTotalSavings = podTotalCurrent - podTotalRecommended;
                const podTotalSavingsPercent = calculateSavingsPercentage(podTotalCurrent, podTotalRecommended);
                podTotalSavingsElements[index].textContent = '$' + podTotalSavings.toFixed(2) + ' (' + podTotalSavingsPercent + '%)';
                if (podTotalSavings > 0) {
                    podTotalSavingsElements[index].classList.add('cost-savings');
                }

                // Add to grand totals
                totalCurrentCost += podTotalCurrent;
                totalRecommendedCost += podTotalRecommended;
            });
        });

        // Update summary totals
        document.getElementById('total-current-cost').textContent = '$' + totalCurrentCost.toFixed(2);
        document.getElementById('total-recommended-cost').textContent = '$' + totalRecommendedCost.toFixed(2);

        const totalSavings = totalCurrentCost - totalRecommendedCost;
        const totalSavingsPercent = calculateSavingsPercentage(totalCurrentCost, totalRecommendedCost);
        document.getElementById('total-savings').textContent = '$' + totalSavings.toFixed(2) + ' (' + totalSavingsPercent + '%)';

        // Calculate resource totals for breakdown
        let totalCurrentCPU = 0;
        let totalRecommendedCPU = 0;
        let totalCurrentMemory = 0;
        let totalRecommendedMemory = 0;
        let containerCount = 0;
        let podCount = document.querySelectorAll('.pod-section').length;
        let namespaces = new Set();

        // Process each pod to get resource totals
        document.querySelectorAll('.pod-section').forEach(function(podSection) {
            // Get CPU resources
            const cpuCurrentElements = podSection.querySelectorAll('.pod-cpu-current-cost');
            const cpuRecommendedElements = podSection.querySelectorAll('.pod-cpu-recommended-cost');

            cpuCurrentElements.forEach(function(element, index) {
                const cpuMillicores = parseFloat(element.getAttribute('data-cpu') || 0);
                const recommendedElement = cpuRecommendedElements[index];
                const recommendedCpuMillicores = parseFloat(recommendedElement.getAttribute('data-cpu') || 0);

                totalCurrentCPU += cpuMillicores / 1000; // Convert millicores to cores
                totalRecommendedCPU += recommendedCpuMillicores / 1000;
            });

            // Get memory resources
            const memoryCurrentElements = podSection.querySelectorAll('.pod-memory-current-cost');
            const memoryRecommendedElements = podSection.querySelectorAll('.pod-memory-recommended-cost');

            memoryCurrentElements.forEach(function(element, index) {
                const memoryMiB = parseFloat(element.getAttribute('data-memory') || 0);
                const recommendedElement = memoryRecommendedElements[index];
                const recommendedMemoryMiB = parseFloat(recommendedElement.getAttribute('data-memory') || 0);

                totalCurrentMemory += memoryMiB / 1024; // Convert MiB to GB
                totalRecommendedMemory += recommendedMemoryMiB / 1024;
            });

            // Count containers
            containerCount += podSection.querySelectorAll('.container-section').length;

            // Track namespaces
            const namespace = podSection.querySelector('.collapsible-header').textContent.split('/')[0].trim();
            namespaces.add(namespace);
        });

        // Update breakdown section
        updateBreakdownSection({
            currentCost: totalCurrentCost,
            recommendedCost: totalRecommendedCost,
            currentCPU: totalCurrentCPU,
            recommendedCPU: totalRecommendedCPU,
            currentMemory: totalCurrentMemory,
            recommendedMemory: totalRecommendedMemory,
            containerCount: Math.round(containerCount / Math.max(1, podCount)), // Average containers per pod
            podCount: podCount,
            namespaceCount: namespaces.size
        });
    }

    // Update the breakdown section with calculated values
    function updateBreakdownSection(data) {
        // Update summary bars
        document.getElementById('current-spend-value').textContent = '$' + data.currentCost.toFixed(2);
        document.getElementById('optimal-spend-value').textContent = '$' + data.recommendedCost.toFixed(2);

        // Set bar widths (max width 100%)
        const maxCost = Math.max(data.currentCost, data.recommendedCost);
        const currentBarWidth = (data.currentCost / maxCost * 100) + '%';
        const optimalBarWidth = (data.recommendedCost / maxCost * 100) + '%';
        document.getElementById('current-spend-bar').style.width = currentBarWidth;
        document.getElementById('optimal-spend-bar').style.width = optimalBarWidth;

        // Update metrics
        const wastePercent = data.currentCost > 0 ?
            Math.round((data.currentCost - data.recommendedCost) / data.currentCost * 100) : 0;
        document.getElementById('waste-percent').textContent = wastePercent + '%';

        // Set efficiency rating (1-10 scale based on waste percent)
        const efficiencyRating = Math.max(1, Math.round(10 - (wastePercent / 10)));
        document.getElementById('efficiency-rating').textContent = efficiencyRating;
        document.getElementById('efficiency-rating').className =
            'breakdown-metric-value ' + (efficiencyRating > 7 ? 'good' : efficiencyRating > 4 ? 'warning' : 'danger');

        // Set monthly shortfall
        const shortfall = data.recommendedCost > data.currentCost ?
            (data.recommendedCost - data.currentCost) : 0;
        document.getElementById('monthly-shortfall').textContent = '$' + shortfall.toFixed(2);

        // Update cluster stats
        document.getElementById('container-count').textContent = data.containerCount;
        document.getElementById('pod-count').textContent = data.podCount;
        document.getElementById('namespace-count').textContent = data.namespaceCount;

        // Calculate resource surplus/deficit
        const cpuSurplus = data.currentCPU - data.recommendedCPU;
        const memorySurplus = data.currentMemory - data.recommendedMemory;

        // Update CPU surplus
        document.getElementById('cpu-surplus').textContent = cpuSurplus.toFixed(2) + ' Cores';
        document.getElementById('cpu-surplus-cost').textContent = '$' + (cpuSurplus * 20).toFixed(2);
        document.getElementById('cpu-indicator').className =
            'resource-indicator ' + (cpuSurplus > 0 ? 'indicator-warning' : cpuSurplus < 0 ? 'indicator-danger' : 'indicator-success');

        // Update Memory surplus
        document.getElementById('memory-surplus').textContent = memorySurplus.toFixed(2) + ' GB';
        document.getElementById('memory-surplus-cost').textContent = '$' + (memorySurplus * 5).toFixed(2);
        document.getElementById('memory-indicator').className =
            'resource-indicator ' + (memorySurplus > 0 ? 'indicator-warning' : memorySurplus < 0 ? 'indicator-danger' : 'indicator-success');

        // Update detailed breakdown
        updateDetailedBreakdown(data);
    }

    // Update the detailed breakdown section
    function updateDetailedBreakdown(data) {
        // Set current values
        document.getElementById('detailed-current-spend-value').textContent = '$' + data.currentCost.toFixed(2);
        document.getElementById('detailed-optimal-spend-value').textContent = '$' + data.recommendedCost.toFixed(2);
        document.getElementById('detailed-current-spend-bar').style.width = '100%';

        // Calculate immediate savings (waste that can be reclaimed)
        const immediateSavings = Math.max(0, data.currentCost - data.recommendedCost);
        document.getElementById('immediate-savings-value').textContent = '$' + immediateSavings.toFixed(2);
        document.getElementById('immediate-savings-bar').style.width =
            (immediateSavings / data.currentCost * 100) + '%';

        // Calculate risk removal (resources that need to be added)
        const riskRemoval = Math.max(0, data.recommendedCost - data.currentCost);
        document.getElementById('risk-removal-value').textContent = '$' + riskRemoval.toFixed(2);
        document.getElementById('risk-removal-bar').style.width =
            (riskRemoval / data.currentCost * 100) + '%';

        // Calculate additional waste (theoretical waste beyond immediate savings)
        const additionalWaste = Math.max(0, (data.currentCost - immediateSavings) - data.recommendedCost);
        document.getElementById('additional-waste-value').textContent = '$' + additionalWaste.toFixed(2);
        document.getElementById('additional-waste-bar').style.width =
            (additionalWaste / data.currentCost * 100) + '%';

        // Set optimal bar width relative to current
        document.getElementById('detailed-optimal-spend-bar').style.width =
            (data.recommendedCost / data.currentCost * 100) + '%';

        // Update CPU breakdown
        document.getElementById('current-cpu').textContent = data.currentCPU.toFixed(2) + ' Cores';
        document.getElementById('current-cpu-cost').textContent = '$' + (data.currentCPU * 20).toFixed(2);

        document.getElementById('optimal-cpu').textContent = data.recommendedCPU.toFixed(2) + ' Cores';
        document.getElementById('optimal-cpu-cost').textContent = '$' + (data.recommendedCPU * 20).toFixed(2);

        // Calculate CPU savings components
        const cpuSavings = Math.max(0, data.currentCPU - data.recommendedCPU);
        document.getElementById('immediate-cpu-savings').textContent = cpuSavings.toFixed(2) + ' Cores';
        document.getElementById('immediate-cpu-savings-cost').textContent = '$' + (cpuSavings * 20).toFixed(2);

        const cpuRisk = Math.max(0, data.recommendedCPU - data.currentCPU);
        document.getElementById('risk-cpu').textContent = cpuRisk.toFixed(2) + ' Cores';
        document.getElementById('risk-cpu-cost').textContent = '$' + (cpuRisk * 20).toFixed(2);

        const cpuWaste = Math.max(0, (data.currentCPU - cpuSavings) - data.recommendedCPU);
        document.getElementById('waste-cpu').textContent = cpuWaste.toFixed(2) + ' Cores';
        document.getElementById('waste-cpu-cost').textContent = '$' + (cpuWaste * 20).toFixed(2);

        // Update Memory breakdown
        document.getElementById('current-memory').textContent = data.currentMemory.toFixed(2) + ' GB';
        document.getElementById('current-memory-cost').textContent = '$' + (data.currentMemory * 5).toFixed(2);

        document.getElementById('optimal-memory').textContent = data.recommendedMemory.toFixed(2) + ' GB';
        document.getElementById('optimal-memory-cost').textContent = '$' + (data.recommendedMemory * 5).toFixed(2);

        // Calculate Memory savings components
        const memorySavings = Math.max(0, data.currentMemory - data.recommendedMemory);
        document.getElementById('immediate-memory-savings').textContent = memorySavings.toFixed(2) + ' GB';
        document.getElementById('immediate-memory-savings-cost').textContent = '$' + (memorySavings * 5).toFixed(2);

        const memoryRisk = Math.max(0, data.recommendedMemory - data.currentMemory);
        document.getElementById('risk-memory').textContent = memoryRisk.toFixed(2) + ' GB';
        document.getElementById('risk-memory-cost').textContent = '$' + (memoryRisk * 5).toFixed(2);

        const memoryWaste = Math.max(0, (data.currentMemory - memorySavings) - data.recommendedMemory);
        document.getElementById('waste-memory').textContent = memoryWaste.toFixed(2) + ' GB';
        document.getElementById('waste-memory-cost').textContent = '$' + (memoryWaste * 5).toFixed(2);
    }

    // Image modal/lightbox functionality
    const modal = document.getElementById('imageModal');
    const modalImg = document.getElementById('modalImage');
    const closeModal = document.querySelector('.close-modal');

    // Get all chart images and add click event listeners
    document.addEventListener('DOMContentLoaded', function() {
        // Find all chart images
        const chartImages = document.querySelectorAll('.chart img, .comparison-chart img');

        // Add click event to each image
        chartImages.forEach(function(img) {
            img.addEventListener('click', function() {
                modal.style.display = 'block';
                modalImg.src = this.src;
                // Add alt text if available
                modalImg.alt = this.alt || '';
            });
        });

        // Close modal when clicking the × button
        closeModal.addEventListener('click', function() {
            modal.style.display = 'none';
        });

        // Close modal when clicking outside the image
        modal.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && modal.style.display === 'block') {
                modal.style.display = 'none';
            }
        });
    });
</script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        function setupCollapsible(headerSelector, activeClassName) {
            var headers = document.querySelectorAll(headerSelector);
            headers.forEach(function(header) {
                header.addEventListener('click', function() {
                    this.classList.toggle(activeClassName);
                    var content = this.nextElementSibling;
                    if (content.style.display === "block") {
                        content.style.display = "none";
                    } else {
                        content.style.display = "block";
                    }
                });
            });
        }

        setupCollapsible('.collapsible-header', 'active'); // For Pod Sections (h2)
        setupCollapsible('.container-collapsible-header', 'active'); // For Container Sections (h5)
    });
</script>
</body>
</html>