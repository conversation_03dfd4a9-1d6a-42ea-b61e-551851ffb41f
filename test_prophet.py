#!/usr/bin/env python
"""
Test script for Prophet ML option.
"""
import os
import datetime
from optimus.cli import main

# Set environment variables for Prophet ML
os.environ["OPTIMUS_ML_MODEL_TYPE"] = "prophet"
os.environ["OPTIMUS_USE_ML"] = "true"

# Run the main function with test arguments
args = [
    "--prometheus-url", "http://localhost:9090",
    "--namespace", "default",
    "--pod", "test-pod",
    "--output-format", "html",
    "--output-file", "prophet_test_report.html",
    "--generate-charts"
]

# Run the main function
main(args)

print(f"Prophet ML test report generated: {os.path.abspath('prophet_test_report.html')}")
