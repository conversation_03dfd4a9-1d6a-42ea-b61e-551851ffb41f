"""
Tests for the CLI module.
"""
import json
import sys
from io import StringIO
from unittest.mock import patch, MagicMock

import pytest
import yaml

from optimus.cli import format_output, write_output, main


class TestCLI:
    """Tests for the CLI module."""

    @pytest.fixture
    def sample_results(self):
        """Create sample analysis results."""
        return [
            {
                "cluster": "test-cluster",
                "namespace": "default",
                "pod": "test-pod",
                "cpu": {
                    "stats": {
                        "min": 0.1,
                        "max": 0.5,
                        "mean": 0.3,
                        "median": 0.3,
                        "p95": 0.5,
                        "p99": 0.5,
                    },
                    "current": {
                        "request": 0.5,
                        "limit": 1.0,
                    },
                    "recommended": {
                        "request": 0.6,
                        "limit": 0.6,
                    },
                    "change": {
                        "request": 20.0,
                        "limit": -40.0,
                    },
                },
                "memory": {
                    "stats": {
                        "min_mib": 100,
                        "max_mib": 300,
                        "mean_mib": 200,
                        "median_mib": 200,
                        "p95_mib": 300,
                        "p99_mib": 300,
                    },
                    "current": {
                        "request_mib": 200,
                        "limit_mib": 300,
                    },
                    "recommended": {
                        "request_mib": 390,
                        "limit_mib": 390,
                    },
                    "change": {
                        "request": 95.0,
                        "limit": 30.0,
                    },
                },
                "current_resources": {
                    "cpu_request": 0.5,
                    "cpu_limit": 1.0,
                    "memory_request_mib": 200,
                    "memory_limit_mib": 300,
                },
            }
        ]

    def test_format_output_json(self, sample_results):
        """Test format_output with JSON format."""
        output = format_output(sample_results, "json")
        parsed = json.loads(output)
        assert len(parsed) == 1
        assert parsed[0]["namespace"] == "default"
        assert parsed[0]["pod"] == "test-pod"

    def test_format_output_yaml(self, sample_results):
        """Test format_output with YAML format."""
        output = format_output(sample_results, "yaml")
        parsed = yaml.safe_load(output)
        assert len(parsed) == 1
        assert parsed[0]["namespace"] == "default"
        assert parsed[0]["pod"] == "test-pod"

    def test_format_output_text(self, sample_results):
        """Test format_output with text format."""
        output = format_output(sample_results, "text")
        assert "Pod: default/test-pod" in output
        assert "CPU:" in output
        assert "Memory:" in output
        assert "Current Request: 0.5 millicores" in output
        assert "Recommended Request: 0.6 millicores" in output
        assert "Current Request: 200 MiB" in output
        assert "Recommended Request: 390 MiB" in output

    def test_write_output_stdout(self, sample_results):
        """Test write_output to stdout."""
        output = format_output(sample_results, "text")

        with patch("sys.stdout", new=StringIO()) as fake_stdout:
            write_output(output)
            assert fake_stdout.getvalue() == output + "\n"

    def test_write_output_file(self, sample_results, tmp_path):
        """Test write_output to file."""
        output = format_output(sample_results, "text")
        output_file = tmp_path / "output.txt"

        write_output(output, str(output_file))

        assert output_file.exists()
        assert output_file.read_text() == output

    def test_main_success(self):
        """Test main function success."""
        # Mock command-line arguments
        with patch("sys.argv", ["optimus"]):
            # Mock configuration
            mock_config = MagicMock()
            mock_config.output_format = "text"
            mock_config.output_file = None
            mock_config.charts_dir = "charts"

            # Mock analyzer
            mock_analyzer = MagicMock()
            mock_analyzer.analyze_all_pods.return_value = [
                {"cluster": "test-cluster", "namespace": "default", "pod": "test-pod", "cpu": {}, "memory": {}},
            ]

            # Mock dependencies
            with patch("optimus.cli.get_config", return_value=mock_config), \
                 patch("optimus.cli.ResourceAnalyzer", return_value=mock_analyzer), \
                 patch("optimus.cli.parse_args") as mock_parse_args, \
                 patch("optimus.cli.generate_charts", return_value={}) as mock_generate_charts, \
                 patch("optimus.cli.format_output", return_value="test output"), \
                 patch("optimus.cli.write_output") as mock_write_output:

                # Mock arguments
                args = MagicMock()
                args.verbose = False
                args.cluster = None
                args.namespace = None
                args.selector = None
                args.time_period = None
                args.output_format = None
                args.output_file = None
                args.generate_charts = True
                args.charts_dir = "test-charts"
                mock_parse_args.return_value = args

                result = main()

                # Check that the functions were called with the correct parameters
                mock_analyzer.analyze_all_pods.assert_called_once()
                mock_generate_charts.assert_called_once_with(mock_analyzer.analyze_all_pods.return_value, "test-charts")
                mock_write_output.assert_called_once_with("test output", None)

                # Check the result
                assert result == 0

    def test_main_error(self):
        """Test main function error handling."""
        # Mock command-line arguments
        with patch("sys.argv", ["optimus"]):
            # Mock an exception
            with patch("optimus.cli.get_config", side_effect=ValueError("Test error")), \
                 patch("optimus.cli.setup_logging"):

                result = main()

                # Check the result
                assert result == 1

    def test_generate_charts(self, sample_results, tmp_path):
        """Test generate_charts function."""
        from optimus.cli import generate_charts

        # Generate charts
        chart_paths = generate_charts(sample_results, str(tmp_path))

        # Check that chart paths were returned
        pod_id = "test-cluster_default_test-pod"
        assert pod_id in chart_paths
        assert "cpu" in chart_paths[pod_id]
        assert "memory" in chart_paths[pod_id]
        assert "comparison" in chart_paths[pod_id]

        # Check that chart files were created
        assert tmp_path.joinpath(pod_id, "cpu_usage.png").exists()
        assert tmp_path.joinpath(pod_id, "memory_usage.png").exists()
        assert tmp_path.joinpath(pod_id, "resource_comparison.png").exists()

    def test_format_output_with_charts(self, sample_results, tmp_path):
        """Test format_output with charts."""
        from optimus.cli import generate_charts

        # Generate charts
        chart_paths = generate_charts(sample_results, str(tmp_path))

        # Format output with charts
        output = format_output(sample_results, "text", chart_paths)

        # Check that chart paths are included in the output
        assert "Charts:" in output
        assert "CPU Usage:" in output
        assert "Memory Usage:" in output
        assert "Resource Comparison:" in output
